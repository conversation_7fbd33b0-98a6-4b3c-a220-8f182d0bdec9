/* ----------------------------------------------------------------------------------------
* Author        : Awaiken
* Template Name : Artistic - Creative Digital Agency HTML Template
* File          : CSS File
* Version       : 1.0
* ---------------------------------------------------------------------------------------- */
/* INDEX
----------------------------------------------------------------------------------------
01. Global Variables
02. General css
03. Header css
04. Hero css
05. Scrolling Ticker css
06. About Agency css
07. Our Services css
08. Digital Success css
09. Why Choose Us css
10. Join <PERSON> css
11. How It Work css
12. Our Features css
13. Our Portfolio css
14. Our Testimonial css
15. Agency Benefits Css
16. Our Blog Css
17. Footer css
18. About Us Page css
19. Services Page css
20. Services Single css
21. Blog Archive css
22. Blog Single css
23. Project Page css
24. Project Single css
25. Team Page css
26. Team Single css
27. Pricing Page css
28. Testimonial Page css
39. Image Gallery css
30. Video Gallery css
31. FAQs Page css
32. Contact Us Page css
33. 404 Page css
34. Responsive css
-------------------------------------------------------------------------------------- */

/************************************/
/*** 	 01. Global Variables	  ***/
/************************************/	

:root{
	--primary-color			: #FFFFFF;
	--secondary-color		: #1B1B1B;
	--text-color			: #F5F5F4;
	--accent-color			: #BFF747;
	--dark-color			: #000000;
	--divider-color			: #FFFFFF1A;
	--dark-divider-color	: #F7FBFA1A;
	--error-color			: rgb(230, 87, 87);
	--default-font			: "Fustat", sans-serif;
}

/************************************/
/*** 	   02. General css		  ***/
/************************************/

body{
	position: relative;
	font-family: var(--default-font);
	font-size: 16px;
	font-weight: 400;
	line-height: 1.6em;
	color: var(--text-color);
	background: url('../images/section-bg-shape.png') var(--dark-color);
	background-repeat: repeat-y;
	background-position: top 900px center;
	background-size: 100% auto;
}

p{
	line-height: 1.7em;
	margin-bottom: 1.6em;
}

h1,
h2,
h3,
h4,
h5,
h6{
	margin :0;
	font-weight: 700;
	line-height: 1.2em;
	color: var(--primary-color);
}

figure{
	margin: 0;
}

img{
	max-width: 100%;
}

a{
	text-decoration: none;
}

a:hover{
	text-decoration: none;
	outline: 0;
}

a:focus{
	text-decoration: none;
	outline: 0;
}

html,
body{
	width: 100%;
	overflow-x: clip;
}

.container{
	max-width: 1300px;
}

.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl{
    padding-right: 15px;
    padding-left: 15px;
}

.image-anime{
	position: relative;
	overflow: hidden;
}

.image-anime:after{
	content: "";
	position: absolute;
    width: 200%;
    height: 0%;
    left: 50%;
    top: 50%;
    background-color: rgba(255,255,255,.3);
    transform: translate(-50%,-50%) rotate(-45deg);
    z-index: 1;
}

.image-anime:hover:after{
    height: 250%;
    transition: all 600ms linear;
    background-color: transparent;
}

.reveal{
	position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    visibility: hidden;
    overflow: hidden;
}

.reveal img{
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -webkit-transform-origin: left;
    transform-origin: left;
}

.row{
    margin-right: -15px;
    margin-left: -15px;
}

.row > *{
	padding-right: 15px;
	padding-left: 15px;
}

.row.no-gutters{
    margin-right: 0px;
    margin-left: 0px;
}

.row.no-gutters > *{
    padding-right: 0px;
    padding-left: 0px;
}

.btn-default{
	display: inline-block;
	font-size: 16px;
	font-weight: 700;
	line-height: 1.4em;
	text-transform: capitalize;
	background: transparent;
	color: var(--primary-color);
	border-radius: 100px;
	padding: 14px 25px;
	margin-right: 50px;
	border: none;
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
	transition: all 0.5s ease-in-out;
	position: relative;
	z-index: 1;
}

.btn-default::after{
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: var(--primary-color);
	border-radius: 100px;
	opacity: 20%;
	width: 100%;
	height: 100%;
}

.btn-default::before{
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	width: 50px;
	height: 50px;
	border-radius: 50%;
	background-color: var(--accent-color);
	background-image: url('../images/arrow-dark.svg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 24px auto;
	transform: translate(50px, 0px);
	transition: all 0.4s ease-in-out;
}

.btn-default:hover::before{
	background-color: var(--primary-color);
	transform: translate(50px, 0px) rotate(45deg);
}

.btn-highlighted{
	position: relative;
    display: inline-block;
    line-height: 1.3em;
	font-size: 16px;
    font-weight: 700;
    background: var(--accent-color);
    color: var(--dark-color);
    text-transform: capitalize;
    border-radius: 10px;
    padding: 15px 30px;
    border: none;
    overflow: hidden;
    transition: all 0.4s ease-in-out;
    z-index: 1;
}

.btn-highlighted:hover{
	background-color: var(--primary-color);
}

.preloader{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	background-color: var(--dark-color);
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-container,
.loading{
	height: 100px;
	position: relative;
	width: 100px;
	border-radius: 100%;
}

.loading-container{
	margin: 40px auto;
}

.loading{
	border: 1px solid transparent;
	border-color: transparent var(--accent-color) transparent var(--accent-color);
	animation: rotate-loading 1.5s linear 0s infinite normal;
	transform-origin: 50% 50%;
}

.loading-container:hover .loading,
.loading-container .loading{
	transition: all 0.5s ease-in-out;
}

#loading-icon{
	position: absolute;
	top: 50%;
	left: 50%;
	max-width: 66px;
	transform: translate(-50%, -50%);
}

@keyframes rotate-loading{
	0%{
		transform: rotate(0deg);
	}

	100%{
		transform: rotate(360deg);
	}
}

.section-row{
	margin-bottom: 80px;
}

.section-row .section-title{
	width: 100%;
	max-width: 605px;
	margin-bottom: 0;
}

.section-title{
	margin-bottom: 40px;
}

.section-title h3{
	display: inline-block;
	position: relative;
	font-size: 14px;
    font-weight: 600;
	line-height: normal;
	letter-spacing: 0.2em;
    text-transform: uppercase;
    color: var(--primary-color);
	padding-left: 24px;
    margin-bottom: 20px;
}

.section-title h3::before{
	content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background: url(../images/icon-sub-accent-heading.svg) no-repeat;
    background-position: left center;
    background-size: cover;
    width: 16px;
    height: 16px;
}

.section-title h1{
	font-size: 120px;
	font-weight: 300;
	line-height: 1.2em;
	color: var(--primary-color);
	margin-bottom: 0;
	cursor: none;
}

.section-title h1 span{
	font-weight: 800;
	color: var(--accent-color);
}

.section-title h2{
	font-size: 50px;
	font-weight: 300;
	line-height: 1.2em;
	color: var(--primary-color);
	margin-bottom: 0;
	cursor: none;
}

.section-title h2 span{
	font-weight: 700;
	color: var(--accent-color);
}

.section-title p{
	margin-top: 20px;
	margin-bottom: 0;
}

.section-content-btn .section-title-content{
	margin-bottom: 30px;
}

.section-title-content p{
	margin: 0;
}

.help-block.with-errors ul{
	margin: 0;
	text-align: left;
}

.help-block.with-errors ul li{
	color: var(--error-color);
	font-weight: 500;
	font-size: 14px;
}

/************************************/
/**** 	   03. Header css		 ****/
/************************************/

header.main-header{
	position: absolute;
	top: 0;
	width: 100%;
	border-bottom: 1px solid var(--divider-color);
	z-index: 100;
}

header.main-header .header-sticky{
	position: relative;
	top: 0;
	z-index: 100;
}

header.main-header .header-sticky.hide{
	transform: translateY(-100%);
	transition: transform 0.3s ease-in-out;
	border-radius: 0;
}

header.main-header .header-sticky.active{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	border-radius: 0;
    transform: translateY(0);
	background: var(--dark-color);
	border-bottom: 1px solid var(--divider-color);
	backdrop-filter: blur(30px);
	-webkit-backdrop-filter: blur(30px);
}

.navbar{
	padding: 25px 0;
	align-items: center;
}

.navbar-brand{
	padding: 0;
	margin: 0;
}

.main-menu .nav-menu-wrapper{
	flex: 1;
	text-align: left;
	margin-left: 40px;
}

.main-menu .nav-menu-wrapper > ul{
	align-items: center;
	display: inline-flex;
}

.main-menu ul li{
	margin: 0;
	position: relative;
}

.main-menu ul li.nav-item a{
	font-size: 16px;
	font-weight: 600;
	padding: 15px !important;
	color: var(--primary-color);
	text-transform: capitalize;
	transition: all 0.3s ease-in-out;
}

.main-menu ul li.submenu > a:after{
	content: '\f107';
	font-family: 'FontAwesome';
	font-weight: 900;
	font-size: 14px;
	margin-left: 8px;
}

.main-menu ul li a:hover,
.main-menu ul li a:focus{
	color: var(--accent-color);
}

.main-menu ul ul{
	visibility: hidden;
	opacity: 0;
	transform: scaleY(0.8);
	transform-origin: top;
	padding: 0;
	margin: 0;
	list-style: none;
	width: 230px;
	border-radius: 20px;
	position: absolute;
	left: 0;
	top: 100%;
	background: var(--accent-color);
	text-align: left;
	transition: all 0.3s ease-in-out;
}

.main-menu ul li.submenu:first-child ul{
    width: 230px;
}

.main-menu ul ul ul{
	left: 100%;
	top: 0;
	text-align: left;
}
.main-menu ul li:hover > ul{
	visibility: visible;
	opacity: 1;
	transform: scaleY(1);
    padding: 5px 0;
}

.main-menu ul li.submenu ul li.submenu > a:after{
    content: '\f105';
    float: right;
}

.main-menu ul ul li{
	margin: 0;
	padding: 0;
}

.main-menu ul ul li.nav-item a{
	color: var(--dark-color);
	padding: 6px 20px !important;
	transition: all 0.3s ease-in-out;
}

.main-menu ul li:hover > ul{
	visibility: visible;
	opacity: 1;
	transform: scaleY(1);
    padding: 5px 0;
}

.main-menu ul ul li a:hover,
.main-menu ul ul li a:focus{
	color: var(--primary-color);
	background-color: transparent;
	padding: 6px 20px 6px 23px !important;
}

.header-social-box{
	align-items: center;
}

.header-social-links{
	margin-right: 20px;
}

.header-social-links ul{
	margin: 0;
	padding: 0;
	list-style: none;
	display: flex;
	align-items: center;
}

.header-social-links ul li{
	line-height: normal;
	margin-right: 15px;
	padding: 0;
}

.header-social-links ul li a{
	color: inherit;
}

.header-social-links ul li:last-child{
	margin: 0;
}

.header-social-links ul li i{
	font-size: 20px;
	color: var(--accent-color);
	transition: all 0.4s ease-in-out;
}

.header-social-links ul li:hover i{
	color: var(--primary-color);
}

.header-btn .btn-popup{
	position: relative;
	display: flex;
	flex-wrap: wrap;
	z-index: 100;
}

.header-btn .offcanvas-backdrop{
	z-index: 100;
}

.header-btn .btn-popup{
	background: var(--divider-color);
	border-radius: 50%;
	border: none;
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.header-btn .btn-popup img{
	max-width: 14px;
}

body:has(:not(.offcanvas)){
	padding-right: initial !important;
}

body:has(.offcanvas){
	padding-right: 0 !important;
}

.header-btn .offcanvas{
	position: fixed;
	top: 0;
	bottom: 0;
	left: auto;
	right: -15px;
	background: var(--secondary-color) !important;
	border-radius: 0;
	opacity: 100%;
	width: 100% !important;
	height: 100% !important;
	max-width: 375px;
	border: none;
	padding: 150px 37px 150px 30px;
	transform: translateX(100%) !important;
	z-index: 101;
	transition: all 0.3s ease-in-out !important;
} 

.offcanvas.show:not(.hiding),
.offcanvas.showing{
    transform: translateX(0) !important;
}

.navbar-expand-lg .offcanvas .offcanvas-body{
	display: block;
}

.header-btn .offcanvas .btn-close{
	position: absolute;
	top: 30px;
	right: 37px;
	background-color: transparent;
	border: 1px solid var(--primary-color);
	border-radius: 50%;
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	filter: brightness(0) invert(1);
	opacity: 100%;
	box-shadow: none;
	padding: 0;
}

.header-contact-box{
	text-align: center;
	border-bottom: 1px solid var(--divider-color);
	padding-bottom: 30px;
	margin-bottom: 30px;
}

.header-contact-box .icon-box{
	margin-bottom: 20px;
}

.header-contact-box .icon-box img{
	max-width: 40px;
}

.header-contact-box-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.header-contact-box-content p{
	color: var(--text-color);
	margin: 0;
}

.header-social-links.sidebar-social-links{
	text-align: center;
	margin-right: 0;
}

.header-social-links.sidebar-social-links h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 20px;
}

.header-social-links.sidebar-social-links ul{
	justify-content: center;
}

.header-social-links.sidebar-social-links ul li a{
	background-color: transparent;
	border: 1px solid var(--accent-color);
	border-radius: 50%;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease-in-out;
}

.header-social-links.sidebar-social-links ul li a:hover{
	border-color: var(--primary-color);
}

.responsive-menu,
.navbar-toggle{
	display: none;
}

.responsive-menu{
	top: 0;
	position: relative;
}

.slicknav_btn{
	background: var(--accent-color);
	padding: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 38px;
	height: 38px;
	margin: 0;
	border-radius: 8px;
}

.slicknav_icon .slicknav_icon-bar{
	display: block;
	width: 100%;
	height: 3px;
	width: 22px;
	background-color: var(--dark-color);
	border-radius: 6px;
	margin: 4px auto !important;
	transition: all 0.1s ease-in-out;
}

.slicknav_icon .slicknav_icon-bar:first-child{
	margin-top: 0 !important;
}

.slicknav_icon .slicknav_icon-bar:last-child{
	margin-bottom: 0 !important;
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(1){
    transform: rotate(-45deg) translate(-5px, 5px);
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(2){
    opacity: 0;
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(3){
    transform: rotate(45deg) translate(-5px, -5px);
}

.slicknav_menu{
	position: absolute;
    width: 100%;
	padding: 0;
	background: var(--accent-color);
}

.slicknav_menu ul{
	margin: 5px 0;
}

.slicknav_menu ul ul{
	margin: 0;
}

.slicknav_nav .slicknav_row,
.slicknav_nav li a{
	position: relative;
	font-size: 16px;
	font-weight: 600;
	text-transform: capitalize;
	padding: 8px 20px;
	color: var(--dark-color);
	line-height: normal;
	margin: 0;
	border-radius: 0 !important;
	transition: all 0.3s ease-in-out;
}

.slicknav_nav a:hover,
.slicknav_nav a:focus,
.slicknav_nav .slicknav_row:hover{
	background-color: transparent;
	color: var(--primary-color);
}

.slicknav_menu ul ul li a{
    padding: 8px 20px 8px 30px;
}

.slicknav_arrow{
	font-size: 0 !important;
}

.slicknav_arrow:after{
	content: '\f107';
	font-family: 'FontAwesome';
	font-weight: 900;
	font-size: 12px;
	margin-left: 8px;
	color: var(--dark-color);
	position: absolute;
	right: 15px;
    top: 15px;
	transition: all 0.3s ease-out;
}

.slicknav_open > a .slicknav_arrow:after{
    transform: rotate(-180deg);
	color: var(--primary-color);
}

/************************************/
/***        04. Hero css	      ***/
/************************************/

.hero{
	position: relative;
	padding: 215px 0 120px;
	min-height: 100vh;
}

.hero .hero-bg-video{
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
}

.hero .hero-bg-video video{
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.hero.bg-image{
	position: relative;
	background: url('../images/hero-bg.jpg') no-repeat;
	background-position: center center;
	background-size: cover;
}

.hero.bg-image::before{
	content: '';
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(270deg, rgba(0, 0, 0, 0) -73.04%, rgba(22, 22, 22, 0.3) 1.71%, var(--secondary-color) 100%);
	width: 100%;
	height: 100%;
	z-index: 0;
}

.hero.hero-slider-layout{
	background: none;
	padding: 0;
}

.hero.hero-slider-layout .hero-slide{
	position: relative;
	background: url('../images/hero-bg.jpg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
    padding: 215px 0 120px;
	min-height: 100vh;
}

.hero.hero-slider-layout .hero-slide.slide-2{
	background: url('../images/hero-bg-2.jpg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
}

.hero.hero-slider-layout .hero-slide::before{
	content: '';
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(270deg, rgba(0, 0, 0, 0) -73.04%, rgba(22, 22, 22, 0.3) 1.71%, var(--secondary-color) 100%);
	width: 100%;
	height: 100%;
	z-index: 1;
}

.hero.hero-slider-layout .hero-slide .hero-slider-image{
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
}

.hero.hero-slider-layout .hero-slide .hero-slider-image img{
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.hero.hero-slider-layout .hero-pagination{
	position: absolute;
    bottom: 30px;
	text-align: center;
	z-index: 2;
}

.hero.hero-slider-layout .hero-pagination .swiper-pagination-bullet{
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    opacity: 1;
    transition: all 0.3s ease-in-out;
    margin: 0 5px;
}

.hero.hero-slider-layout .hero-pagination .swiper-pagination-bullet-active{
    background-color: var(--accent-color);
}

.hero-content{
	position: relative;
	z-index: 1;
}

.hero-content .section-title{
	width: 100%;
	margin-bottom: 70px;
}

.typing-title{
	display: none;
}

.section-title h1 span div{
	color: transparent;
}

.hero-content-body{
	display: flex;
	align-items: center;
	margin-left: 150px;
}

.hero-content-video{
	width: 35%;
	display: flex;
	align-items: center;
}

.hero-content-video .video-play-button a{
	background: var(--accent-color);
	border-radius: 50%;
	height: 100px;
	width: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: none;
	transition: all 0.3s ease-in-out;
}

.hero-content-video .video-play-button:hover a{
	background-position: right center;
}

.hero-content-video .video-play-button a i{
	font-size: 35px;
	color: var(--dark-color);
}

.learn-more-circle{
	margin-left: -20px;
}

.learn-more-circle img{
	max-width: 100px;
	animation: infiniterotate 30s infinite linear;
}

@keyframes infiniterotate{
	from{
		transform: rotate(0deg);
	  }
	to{
		transform: rotate(360deg);
	}
}

.hero-video-content{
	width: 65%;
}

.hero-video-content p{
	margin: 0;
}

.hero-btn{
	text-align: center;
	margin-top: 60px;
}

/************************************/
/***   05. Scrolling Ticker css   ***/
/************************************/

.our-scrolling-ticker{
	background-color: var(--accent-color);
	padding: 26px 0;
}

.scrolling-ticker-box{
	--gap: 40px;
	position: relative;
	display: flex;
	overflow: hidden;
	user-select: none;
	gap: var(--gap);
	align-items: center;
}

.scrolling-content{
	flex-shrink: 0;
	display: flex;
	gap: var(--gap);
	min-width: 100%;
	animation: scroll 40s linear infinite;
}

.scrolling-content span{
	display: inline-block;
	font-size: 40px;
	font-weight: 700;
	line-height: 1.2em;
	color: var(--dark-color);
	vertical-align: middle;
}

.scrolling-content span img{
	width: 100%;
	max-width: 24px;
	margin-right: 40px;
}

@keyframes scroll{
	from{
		transform: translateX(0);
	}

	to{
		transform: translateX(calc(-100% - var(--gap)));
	}
}

/************************************/
/***     06. About Agency css	  ***/
/************************************/

.about-agency{
	position: relative;
	padding: 160px 0 80px;
}

.about-agency::before{
	content: '';
	display: block;
	position: absolute;
	left: -120px;
	top: 50%;
	background: url('../images/about-agency-bg.png') no-repeat;
	background-position: left center;
	background-size: contain;
	opacity: 50%;
	width: 386px;
	height: 400px;
	animation: circlerotate 20s infinite linear;
	z-index: -1;
}

@keyframes circlerotate{
	from{
		transform: rotate(0deg);
	  }
	to{
		transform: rotate(360deg);
	}
}

.about-agency-content{
	position: sticky;
	top: 100px;
	padding-right: 35px;
}

.about-agency-list{
	border-left: 1px solid var(--divider-color);
	display: flex;
	flex-wrap: wrap;
	gap: 50px 30px;
	padding-left: 50px;
}

.about-agency-item{
	width: 100%;
}

.about-agency-item .icon-box{
	margin-bottom: 20px;
}

.about-agency-item .icon-box img{
	max-width: 40px;
}

.agency-item-content h3{
	position: relative;
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.agency-item-content h3:after{
	content: '';
	position: absolute;
	top: 0;
	left: -60px;
	background: url('../images/dot-green-icon.svg') no-repeat;
	background-position: left center;
	background-size: cover;
	width: 20px;
	height: 20px;
}

.agency-item-content p:last-child{
	margin: 0;
}

/************************************/
/***     07. Our Services css     ***/
/************************************/

.our-services{
	padding: 80px 0;
}

.service-item{
	position: relative;
	background: url('../images/service-bg.svg') no-repeat;
	background-position: top left;
	background-size: auto;
	border: 1px solid var(--divider-color);
	border-radius: 30px;
	backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
	height: calc(100% - 30px);
	margin-bottom: 30px;
	padding: 40px;
	transition: all 0.4s ease-in-out;
	overflow: hidden;
}

.service-item:hover{
	transform: translateY(-5px);
}

.service-item::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100%;
	background: var(--secondary-color);
	opacity: 40%;
}

.service-item-header{
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 100px;
}

.service-item-header .icon-box img{
	max-width: 60px;
}

.service-arrow a img{
	max-width: 34px;
	transition: all 0.3s ease-in-out;
}

.service-item:hover .service-arrow a img{
	filter: brightness(0) invert(1);
	transform: rotate(45deg);
}

.service-item-body{
	position: relative;
}

.service-item-body h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 20px;
}

.service-item-body p{
	margin-bottom: 0;
}

.service-footer{
	margin-top: 20px;
}

.service-footer p{
	text-align: center;
	margin-bottom: 0;
}

.service-footer p a{
	font-weight: 700;
	text-transform: capitalize;
	text-decoration: underline;
	color: var(--accent-color);
	transition: all 0.3s ease-in-out;
}

.service-footer p a:hover{
	color: var(--primary-color);
}

/************************************/
/***    09. Digital Success css   ***/
/************************************/

.digital-success{
	padding: 80px 0;
}

.digital-success-box{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 80px;
	padding: 80px;
	position: relative;
	border-radius: 30px;
	background: linear-gradient(180deg, #BFF747 0%, rgba(0, 0, 0, 0) 90.04%);
}

.digital-success-box::before{
	content: "";
    position: absolute;
	left: 20px;
	top: 20px;
	border-radius: 16px;
	background: linear-gradient(180deg, #000000 75%, rgba(0, 0, 0, 0) 99.04%);
	width: calc(100% - 40px);
	height: calc(100% - 20px);
}

.digital-success-list,
.digital-success-content{
	position: relative;
	width: calc(50% - 40px);
	z-index: 1;
}

.digital-success-content::before{
	content: '';
    display: block;
    position: absolute;
    top: 0;
    right: -40px;
    border: 1px dashed var(--dark-divider-color);
    height: 100%;
    transition: all 0.4s ease-in-out;
}

.digital-success-content .section-title{
	margin-bottom: 80px;
}

.success-counter-box{
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
}

.success-counter-item{
	width: calc(50% - 15px);
}

.success-counter-item h2{
	font-size: 50px;
	color: var(--accent-color);
	margin-bottom: 10px;
}

.success-counter-item p{
	margin: 0;
}

.success-list-item{
	background: url('../images/icon-sub-accent-heading.svg') no-repeat;
	background-position: left top 2px;
	background-size: 20px auto;
	padding-left: 30px;
	margin-bottom: 80px;
}

.success-list-item:last-child{
	margin-bottom: 0;
}

.success-list-item p{
	margin: 0;
}

.success-list-item p span{
	font-size: 22px;
	font-weight: 700;
	text-transform: capitalize;
}

/************************************/
/***     10. Why Choose Us css    ***/
/************************************/

.why-choose-us{
	position: relative;
	padding: 80px 0;
}

.why-choose-us::before{
    content: '';
    display: block;
    position: absolute;
    right: -90px;
    top: 50%;
    background: url(../images/why-choose-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 310px;
    height: 325px;
    animation: circlezoomrotate 10s infinite linear;
    animation-direction: alternate;
    z-index: -1;
}

@keyframes circlezoomrotate{
	from{
		transform: rotate(0deg) scale(0.5);
	  }
	to{
		transform: rotate(360deg) scale(1);
	}
}

.why-choose-content{
	margin-right: 30px;
}

.why-choose-item{
	position: relative;
	border: 1px solid var(--divider-color);
	border-radius: 20px;
	overflow: hidden;
	margin-bottom: 30px;
	padding: 25px 30px;
}

.why-choose-item:last-child{
	margin-bottom: 0;
}

.why-choose-item::before{
	content: '';
    display: block;
    position: absolute;
    top: 0;
	bottom: 0;
	left: 0;
    right: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 50%, rgba(0, 0, 0, 0) 100%);
	width: 0;
    height: 100%;
    transition: all 0.4s ease-in-out;
}

.why-choose-item.active:before,
.why-choose-item:hover:before{
	width: 100%;
}

.why-choose-item h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.why-choose-item p{
	margin: 0;
}

.why-choose-image figure{
	display: block;
	border-radius: 30px;
	overflow: hidden;
}

.why-choose-image img{
	width: 100%;
	aspect-ratio: 1 / 0.93;
	object-fit: cover;
	border-radius: 30px;
}

/************************************/
/***      10. Join Agency css     ***/
/************************************/

.join-agency{
	padding: 80px 0 50px;
}

.agency-social-item{
	position: relative;
	border: 1px solid var(--divider-color);
	border-radius: 100px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin: 30px 0 30px 30px;
	padding: 30px 40px 30px 70px;
	transition: all 0.3s ease-in-out;
}

.agency-social-item .icon-box{
	position: absolute;
	top: -30px;
	left: -30px;
}

.agency-social-item .icon-box a{
	position: relative;
	display: block;
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 80px;
	height: 80px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	transition: all 0.5s ease-in-out;
}

.agency-social-item:hover .icon-box a{
	background-color: transparent;
}

.agency-social-item .icon-box a::before{
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--secondary-color);
    border-radius: 50%;
    transform: translate(100%, 100%);
    transition: all 0.4s ease-in-out;
}

.agency-social-item:hover .icon-box a::before{
    transform: translate(0);
}

.agency-social-item .icon-box i{
	position: relative;
	font-size: 40px;
	color: var(--dark-color);
	transition: all 0.4s ease-in-out;
	z-index: 1;
}

.agency-social-item:hover .icon-box a i{
	color: var(--primary-color);
}

.agency-social-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 5px;
}

.agency-social-content p{
	margin: 0;
}

.agency-social-btn{
	margin-left: 10px;
}

.readmore-btn{
	border: 1px solid var(--primary-color);
	border-radius: 50%;
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease-in-out;
}

.readmore-btn:hover{
	border-color: var(--accent-color);
}

.readmore-btn img{
	max-width: 24px;
	transition: all 0.3s ease-in-out;
}

.agency-social-item:hover .readmore-btn img{
	transform: rotate(45deg);
}

/************************************/
/***     11. How It Work css      ***/
/************************************/

.how-it-work{
	position: relative;
	padding: 80px 0 50px;
}

.how-it-work::before{
    content: '';
    display: block;
    position: absolute;
    left: -90px;
    top: 40%;
    background: url(../images/how-work-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 297px;
    height: 340px;
    animation: roundrotate 8s infinite linear;
    animation-direction: alternate;
    z-index: -1;
}

@keyframes roundrotate{
	from{
		transform: translateY(0) rotate(0);
	  }
	to{
		transform: translateY(100px) rotate(360deg);
	}
}

.work-process-item{
	position: relative;
    background: url(../images/service-bg.svg) no-repeat;
    background-position: top left;
    background-size: auto;
    border: 1px solid var(--divider-color);
    border-radius: 30px;
    backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
    height: calc(100% - 30px);
    margin-bottom: 30px;
    padding: 40px;
    transition: all 0.4s ease-in-out;
    overflow: hidden;
}

.work-process-item:hover{
	transform: translateY(-5px);
}

.work-process-item::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
	z-index: -1;
}

.work-process-header{
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 40px;
}

.work-process-title h3{
	font-size: 22px;
	text-transform: capitalize;
}

.work-process-btn{
	margin-left: 10px;
}

.work-process-item:hover .work-process-btn .readmore-btn img{
	transform: rotate(45deg);
}

.work-process-content{
	margin-bottom: 80px;
}

.work-process-content p{
	margin: 0;
}

.work-process-body{
	display: flex;
	align-items: center;
	padding-top: 20px;
}

.work-process-no{
	width: 50%;
	padding-right: 10px;
}

.work-process-no h3{
	font-size: 22px;
	text-transform: uppercase;
	margin-bottom: 5px;
}

.work-process-no h2{
	font-size: 40px;
	color: var(--accent-color);
}

.work-process-icon-box{
	width: 50%;
	position: absolute;
	display: block;
	bottom: 0;
	right: 0;
	text-align: center;
	background-color: var(--accent-color);
	border-radius: 30px 0 30px 0;
	padding: 40px;
	overflow: hidden;
}

.work-process-icon-box::before{
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-color);
    border-radius: 30px 0 30px 0;
    transform: translate(100%, 100%);
    transition: all 0.4s ease-in-out;
}

.work-process-item:hover .work-process-icon-box::before{
	transform: translate(0);
}

.work-process-icon-box img{
	position: relative;
	max-width: 60px;
}

/************************************/
/***     12. Our Features css     ***/
/************************************/

.our-features{
	position: relative;
	padding: 80px 0;
}

.our-features::before{
    content: '';
    display: block;
    position: absolute;
    right: -90px;
    top: 50%;
    background: url(../images/features-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 300px;
    height: 285px;
    animation: squrerotate 5s infinite linear;
	animation-iteration-count: infinite;
	animation-direction: alternate;
    z-index: -1;
}

@keyframes squrerotate{
	from{
		transform: translate(0, 0) rotate(0deg);
	  }
	to{
		transform: translate(-150px, 200px) rotate(180deg);
	}
}

.digital-features-box{
	height: 100%;
	display: flex;
	flex-wrap: wrap;
	gap: 60px;
}

.digital-features-item.features-item-1{
	width: calc(60% - 30px);
}

.digital-features-item.features-item-2{
	width: calc(40% - 30px);
}

.digital-features-item{
	position: relative;
    padding: 60px;
    border: 1px solid var(--divider-color);
	backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
    border-radius: 30px;
	overflow: hidden;
}

.digital-features-item::before{
    content: '';
	display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: var(--secondary-color);
    opacity: 40%;
	z-index: 0;
}

.digital-features-image{
	position: relative;
	border-radius: 30px;
	overflow: hidden;
	margin-bottom: 40px;
	z-index: 1;
}

.digital-features-image img{
	width: 100%;
    object-fit: cover;
	border-radius: 30px;
	transition: all 0.4s ease-in-out;
}

.digital-features-item.features-item-1 .digital-features-image img{
	aspect-ratio: 1 / 0.46;
}

.digital-features-item.features-item-2 .digital-features-image img{
	aspect-ratio: 1 / 0.72;
}

.digital-features-item:hover .digital-features-image img{
	transform: scale(1.1);
}

.digital-features-content{
	position: relative;
	z-index: 1;
}

.digital-features-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.digital-features-content p{
	margin: 0;
}

.digital-features-item.agency-supports{
	padding: 60px 0;
}

.agency-supports-header{
	position: relative;
	display: flex;
	align-items: center;
	margin-bottom: 50px;
	padding: 0 60px;
	z-index: 1;
}

.agency-supports-content{
	width: 50%;
}

.agency-supports-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.agency-supports-content p{
	margin: 0;
}

.agency-free-consultation{
	width: 50%;
	text-align: right;
}

.agency-free-consultation img{
	max-width: 100px;
	animation: infiniterotate 30s infinite linear;
}

.agency-supports-slider{
	position: relative;
}

.agency-supports-slider::before{
	content: '';
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	background: linear-gradient(280deg, #030709 0%, rgba(3, 7, 9, 0) 60.97%);
	width: 215px;
	height: 100%;
	z-index: 2;
}

.agency-supports-slider::after{
	content: '';
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	background: linear-gradient(90deg, #030709 0%, rgba(3, 7, 9, 0) 60.97%);
	width: 215px;
	height: 100%;
	z-index: 2;
}

.agency-supports-logo{
	background-color: var(--secondary-color);
	border-radius: 10px;
	text-align: center;
	padding: 25px 30px;
}

.agency-supports-logo img{
	max-height: 30px;
}

/************************************/
/***    13. Our Portfolio css     ***/
/************************************/

.our-portfolio{
	padding: 80px 0 40px;
}

.our-Project-nav{
	text-align: center;
	margin-bottom: 80px;
}

.our-Project-nav ul{
	list-style: none;
	text-align: center;
	display: inline-flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
	gap: 10px 20px;
	padding: 0;margin: 0;
}

.our-Project-nav ul li a{
	position: relative;
	display: inline-block;
	background-color: transparent;
	border: 1px solid var(--divider-color);
	border-radius: 10px;
	color: var(--text-color);
	font-weight: 400;
	line-height: 1.2em;
	text-transform: capitalize;
	transition: all 0.3s ease-in-out;
	padding: 10px 20px;
    overflow: hidden;
}

.our-Project-nav ul li a.active-btn,
.our-Project-nav ul li a:hover{
	background-color: var(--accent-color);
	color: var(--dark-color);
}

.project-item{
	height: calc(100% - 40px);
	margin-bottom: 40px;
	text-align: center;
	overflow: hidden;
}

.project-image{
	position: relative;
	margin-bottom: 20px;
}

.project-image figure{
	display: block;
	border-radius: 30px;
	overflow: hidden;
}

.project-image img{
	width: 100%;
	aspect-ratio: 1 / 0.8;
	object-fit: cover;
	border-radius: 30px;
	transition: all 0.4s ease-in-out;
}

.project-item:hover .project-image figure img{
	transform: scale(1.1);
}

.project-tag{
	position: absolute;
	top: 25px;
	right: 25px;
	z-index: 1;
}

.project-tag a{
	display: block;
	background-color: var(--divider-color);
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
	color: var(--text-color);
	border-radius: 6px;
	font-size: 14px;
	text-transform: capitalize;
	padding: 6px 15px;
}

.project-btn{
	position: absolute;
	top: 50%;
	left: 50%;
	opacity: 0;
	visibility: hidden;
	transform: translate(-50%, -30%);
	transition: all 0.3s ease-in-out;
	z-index: 1;
}

.project-item:hover .project-btn{
	transform: translate(-50%, -50%);
	opacity: 1;
	visibility: visible;
} 

.project-btn a{
	border: 1px solid var(--primary-color);
	border-radius: 50%;
	width: 100px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease-in-out;
}

.project-btn a:hover{
	background-color: var(--accent-color);
	border-color: var(--accent-color);
}

.project-btn a img{
	max-width: 42px;
	transition: all 0.3s ease-in-out;
}

.project-btn a:hover img{
	filter: brightness(1) invert(1);
	transform: rotate(45deg);
}

.project-content h3{
	font-size: 22px;
	text-transform: capitalize;
}

/************************************/
/***    14. Our Testimonial Css   ***/
/************************************/

.our-testimonial{
	position: relative;
	padding: 80px 0;
}

.our-testimonial::before{
    content: '';
    display: block;
    position: absolute;
    left: -70px;
    top: 50%;
    background: url(../images/testimonial-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 70%;
    width: 306px;
    height: 304px;
    animation: circlerotate 20s infinite linear;
    z-index: -1;
}

.our-testimonial .section-row .section-title{
	max-width: 100%;
}

.testimonial-review-box{
	position: relative;
	width: 100%;
	border: 1px solid var(--divider-color);
	border-radius: 30px;
	backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
	text-align: center;
	overflow: hidden;
	padding: 50px;
}

.testimonial-review-box::before{
	content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: var(--secondary-color);
    opacity: 40%;
    z-index: -1;
}

.testimonial-review-header{
	margin-bottom: 30px;
}

.testimonial-review-header h2{
	font-size: 80px;
	font-weight: 600;
}

.testimonial-review-header .testimonial-rating{
	margin-bottom: 10px;
}

.testimonial-review-header p{
	margin: 0;
}

.testimonial-review-content{
	margin-bottom: 30px;
}

.testimonial-review-content h3{
	font-size: 22px;
}

.company-client-images{
	margin-bottom: 10px;
}

.satisfy-client-image{
    display: inline-block;
    margin-left: -14px;
    border: 1px solid var(--dark-color);
    border-radius: 50%;
    overflow: hidden;
}

.satisfy-client-image:first-child{
    margin: 0;
}

.satisfy-client-image figure{
	display: block;
}

.satisfy-client-image img{
    max-width: 40px;
}

.testimonial-slider{
	margin-left: 30px;
}

.testimonial-slider .swiper-wrapper{
	cursor: none;
}

.testimonial-company-logo{
	margin-bottom: 30px;
}

.testimonial-company-logo img{
	max-width: 120px;
	max-height: 30px;
}

.testimonial-rating{
	margin-bottom: 20px;
}

.testimonial-rating i{
	font-size: 16px;
	color: var(--accent-color);
	margin-right: 2px;
}

.testimonial-rating i:last-child{
	margin-right: 0;
}

.testimonial-content{
	margin-bottom: 40px;
}

.testimonial-content p{
	font-size: 22px;
	font-weight: 600;
	color: var(--primary-color);
	margin-bottom: 0;
}

.testimonial-body{
	display: flex;
	align-items: center;
}

.author-image{
	margin-right: 15px;
}

.author-image figure{
	display: block;
	border-radius: 50%;
	overflow: hidden;
}

.author-image img{
	max-width: 60px;
	border-radius: 50%;
}

.author-content{
	width: calc(100% - 75px);
}

.author-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 5px;
}

.author-content p{
	text-transform: capitalize;
	margin: 0;
}

.testimonial-btn{
	position: absolute;
	bottom: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: right;
	margin-top: 30px;
	z-index: 2;
}

.testimonial-slider .testimonial-button-next,
.testimonial-slider .testimonial-button-prev{
	position: relative;
	width: 60px;
	height: 60px;
	background: var(--secondary-color);
	border-radius: 14px;
	transition: all 0.4s ease-in-out;
}

.testimonial-slider .testimonial-button-next{
	margin-left: 30px;
}


.testimonial-slider .testimonial-button-next:hover,
.testimonial-slider .testimonial-button-prev:hover{
	background: var(--accent-color);
}

.testimonial-slider .testimonial-button-next::before,
.testimonial-slider .testimonial-button-prev::before{
	content: '';
	position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: url("../images/arrow-white.svg") no-repeat center center;
    background-size: 30px auto;
    display: flex;
    align-items: center;
    justify-content: center;
	transform: rotate(45deg);
    transition: all 0.4s ease-in-out;
}

.testimonial-slider .testimonial-button-prev::before{
	transform: rotate(225deg);
}

.testimonial-slider .testimonial-button-next:hover:before,
.testimonial-slider .testimonial-button-prev:hover:before{
	filter: brightness(1) invert(1);
}

.testimonial-benefits-box{
	position: relative;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 150px;
	backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
	border: 1px solid var(--divider-color);
	border-radius: 30px;
	overflow: hidden;
	margin-top: 100px;
	padding: 60px;
}

.testimonial-benefits-box::before{
	content: '';
    display: block;
    position: absolute;
    top: 0;
	bottom: 0;
	left: 0;
    right: 0;
    background: var(--secondary-color);
    opacity: 40%;
    z-index: -1;
}

.testimonial-benefits-item{
	position: relative;
	width: calc(25% - 112.5px);
}

.testimonial-benefits-item::before{
	content: '';
    display: block;
    position: absolute;
	top: 50%;
    right: -75px;
	transform: translateY(-50%);
    border-right: 1px solid var(--divider-color);
	height: 80%;
	z-index: 1;
}

.testimonial-benefits-item:last-child:before{
	display: none;
}

.testimonial-benefits-item .icon-box{
	margin-bottom: 30px;
}

.testimonial-benefits-item .icon-box img{
	max-width: 40px;
}

.testimonial-benefits-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 20px;
}

.testimonial-benefits-content ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

.testimonial-benefits-content ul li{
	position: relative;
    padding-left: 25px;
	margin-bottom: 15px;
}

.testimonial-benefits-content ul li::before{
	content: "\2a";
    font-family: "FontAwesome";
    position: absolute;
    top: 0;
    left: 0;
    font-size: 16px;
    color: var(--primary-color);
}

.testimonial-benefits-content ul li:last-child{
	margin-bottom: 0
}

/************************************/
/***    15. Agency Benefits Css   ***/
/************************************/

.agency-benefits{
	position: relative;
	padding: 80px 0 50px;
}

.agency-benefits::before{
    content: '';
    display: block;
    position: absolute;
    right: -90px;
    top: 10%;
    background: url(../images/agency-benefits-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 279px;
    height: 287px;
    animation: circlerotate 12s infinite linear;
    z-index: 0;
}

.benefits-steps-item{
	position: relative;
    width: 100%;
    border: 1px solid var(--divider-color);
    border-radius: 30px;
    backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
    overflow: hidden;
	height: calc(100% - 30px);
	margin-bottom: 30px;
    padding: 30px 25px;
}

.benefits-steps-item::before{
    content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: var(--secondary-color);
    opacity: 40%;
    z-index: -1;
}

.benefits-steps-no{
	margin-bottom: 40px;
}

.benefits-steps-no h3{
	font-size: 22px;
}

.benefits-steps-item .icon-box{
	margin-bottom: 40px;
}

.benefits-steps-item .icon-box img{
	max-width: 60px;
}

.benefits-steps-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.benefits-steps-content p{
	margin: 0;
}

/************************************/
/***      16. Our Blog Css        ***/
/************************************/

.our-blog{
	padding: 80px 0 40px;
}

.our-blog .section-row .section-title{
	max-width: 100%;
}

.post-item{
	height: calc(100px - 30px);
	margin-bottom: 30px;
}

.post-featured-image{
	margin-bottom: 30px;
}

.post-featured-image figure a{
    cursor: none;
    display: block;
	border-radius: 30px;
    overflow: hidden;
}

.post-featured-image img{
    aspect-ratio: 1 / 0.75;
    object-fit: cover;
	border-radius: 30px;
    transition: all 0.5s ease-in-out;
}

.post-item:hover .post-featured-image img{
	transform: scale(1.1);
}

.post-item-content{
	margin-bottom: 20px;
}

.post-item-content h3{
    font-size: 22px;
}

.post-item-content h3 a{
    color: inherit;
}

.post-item-btn a{
    position: relative;
    display: inline-block;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.2em;
    text-transform: capitalize;
    color: var(--accent-color);
    padding-right: 30px;
    border: none;
}

.post-item-btn a:hover{
	color: var(--primary-color);
}

.post-item-btn a::before{
	content: '';
    position: absolute;
    top: -2px;
    right: 0;
    background-image: url(../images/arrow-accent.svg);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
	width: 24px;
    height: 24px;
    transition: all 0.3s ease-in-out;
}

.post-item-btn a:hover::before{
	filter: brightness(0) invert(1);
    transform: rotate(45deg);
}

/************************************/
/***   	   17. Footer css         ***/
/************************************/

.footer-work-together{
	position: relative;
	padding: 80px 0;
}

.footer-work-together::before{
    content: '';
    display: block;
    position: absolute;
    left: -120px;
    top: 20%;
    background: url(../images/work-together-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 40%;
    width: 305px;
    height: 315px;
    animation: circlmoveerotate 12s infinite linear;
	animation-direction: alternate;
    z-index: -1;
}

@keyframes circlmoveerotate{
	from{
		transform: translateY(0) rotate(0deg);
	  }
	to{
		transform: translateY(160px) rotate(360deg);
	}
}

.work-together-box{
	position: relative;
}

.work-together-content{
	position: relative;
	background: url('../images/work-together-bg.png') no-repeat;
	background-position: center center;
	background-size: auto;
	text-align: center;
}

.work-together-content::before{
	content: '';
    display: block;
    position: absolute;
    left: 0;
	right: 0;
    top: 0;
	bottom: 0;
    background: url(../images/work-together-bg-gradient.png) no-repeat;
    background-position: center center;
    background-size: contain;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.footer-work-together{
	margin-bottom: 0;
}

.footer-work-together h3{
	font-size: 22px;
	text-transform: uppercase;
	margin-bottom: 20px;
}

.footer-work-together h2{
	font-size: 180px;
	font-weight: 800;
    text-transform: uppercase;
}

.work-together-btn{
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.footer-work-together .work-together-btn a{
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 150px;
	height: 150px;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
	align-content: center;
	text-align: center;
	transition: all 0.3s ease-in-out;
}

.footer-work-together .work-together-btn a:hover{
	background-color: var(--secondary-color);
}

.footer-work-together .work-together-btn a img{
	width: 100%;
	max-width: 24px;
	height: 24px;
	color: var(--secondary-color);
	transition: all 0.3s ease-in-out;
}

.footer-work-together .work-together-btn a:hover img{
	filter: brightness(0) invert(1);
	transform: rotate(45deg);
}

.footer-work-together .work-together-btn a span{
	width: 100%;
	font-weight: 700;
	text-transform: capitalize;
	color: var(--dark-color);
	transition: all 0.3s ease-in-out;
}

.footer-work-together .work-together-btn a:hover span{
	color: var(--primary-color);
}

.footer-main{
	border-top: 1px solid var(--divider-color);
	padding: 60px 0 0;
}

.footer-logo{
	margin-bottom: 60px;
}

.footer-contact-item{
	display: flex;
	align-items: center;
	margin-bottom: 30px;
}

.footer-contact-item:last-child{
	margin-bottom: 0;
}

.footer-contact-item .icon-box{
	border: 1px solid var(--accent-color);
	border-radius: 50%;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20px;
	transition: all 0.3s ease-in-out;
}

.footer-contact-item:hover .icon-box{
	border-color: var(--primary-color);
}

.footer-contact-item .icon-box img{
	max-width: 18px;
	transition: all 0.3s ease-in-out;
}

.footer-contact-item:hover .icon-box img{
	filter: brightness(0) invert(1)
}

.footer-contact-content{
	width: calc(100% - 60px);
}

.footer-contact-content p{
	font-size: 22px;
	font-weight: 600;
	margin: 0;
}

.footer-links h3{
	font-size: 22px;
	text-transform: capitalize;
	color: var(--primary-color);
	margin-bottom: 40px;
}

.footer-links ul{
	list-style: none;
	margin: 0;
	padding: 0;
}

.footer-links ul li{
	text-transform: capitalize;
	margin-bottom: 15px;
}

.footer-links ul li:last-child{
	margin-bottom: 0;
}

.footer-links ul li a{
	color: var(--primary-color);
	transition: all 0.3s ease-in-out;
}

.footer-links ul li:hover a{
	color: var(--accent-color);
}

.footer-newsletter-form .form-group{
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.footer-newsletter-form h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 40px;
}

.footer-newsletter-form .form-group .form-control{
	width: 64%;
	padding: 12px 20px;
	border: 1px solid var(--divider-color);
	background: transparent;
	color: var(--text-color);
	border-radius: 10px;
	box-shadow: none;
}

.footer-newsletter-form .form-group .form-control::placeholder{
	color: var(--text-color);
}

.footer-newsletter-form .btn-highlighted{
	margin-left: 10px;
}

.footer-social-links{
	margin-top: 40px;
}

.footer-social-links ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

.footer-social-links ul li{
	display: inline-block;
	border-radius: 50%;
	margin-right: 15px;
}

.footer-social-links ul li:last-child{
	margin-right: 0;
}

.footer-social-links ul li a{
	border: 1px solid var(--accent-color);
	border-radius: 50%;
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease-in-out;
}

.footer-social-links ul li:hover a{
	border-color: var(--primary-color);
}

.footer-social-links ul li a i{
	color: var(--accent-color);
	font-size: 18px;
	transition: all 0.3s ease-in-out;
}

.footer-social-links ul li:hover a i{
	color: var(--primary-color);
}

.footer-copyright{
	border-top: 1px solid var(--dark-divider-color);
	padding: 60px 0;
	margin-top: 60px;
}

.footer-copyright-text{
	text-align: center;
}

.footer-copyright-text p{
	margin: 0;
}

/************************************/
/***     18. About Us Page css    ***/
/************************************/

.page-header{
	position: relative;
	background: url('../images/page-header-bg.jpg') no-repeat center center;
	background-size: cover;
	padding: 245px 0 150px;
}

.page-header-box{
	text-align: center;
}

.page-header-box h1{
	display: inline-block;
	font-size: 80px;
	font-weight: 300;
	line-height: 1.1em;
	color: var(--primary-color);
	margin-bottom: 10px;
	cursor: none;
}

.page-header-box h1 span{
	color: var(--accent-color);
	font-weight: 800;
}

.page-header-box ol{
	margin: 0;
	padding: 0;
	justify-content: center;
}

.page-header-box ol li.breadcrumb-item{
	font-size: 18px;
	font-weight: 600;
	text-transform: capitalize;
	color: var(--primary-color);
}

.page-header-box ol li.breadcrumb-item a{
    color: inherit;
}

.page-header-box ol li.breadcrumb-item.active{
	color: var(--accent-color);
}

.page-header-box ol .breadcrumb-item+.breadcrumb-item::before{
	content: "\2a";
    font-family: "FontAwesome";
	font-size: 10px;
    color: var(--primary-color);
}

.our-scrolling-ticker.subpages-scrolling-ticker{
    padding: 15px 0;
}

.our-scrolling-ticker.subpages-scrolling-ticker .scrolling-ticker-box{
    --gap: 20px;
}

.our-scrolling-ticker.subpages-scrolling-ticker .scrolling-content span{
	font-size: 22px;
}

.our-scrolling-ticker.subpages-scrolling-ticker .scrolling-content span img{
    max-width: 18px;
    margin-right: 20px;
}

.our-approach{
	padding: 80px 0 50px;
}

.mission-vision-item{
	position: relative;
	border: 1px solid var(--divider-color);
	backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
	border-radius: 30px;
	height: calc(100% - 30px);
	margin-bottom: 30px;
	padding: 40px;
    overflow: hidden;
}

.mission-vision-item::before{
    content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: var(--secondary-color);
    opacity: 40%;
    z-index: -1;
}

.mission-vision-image{
	position: relative;
	border-radius: 20px;
	margin-bottom: 30px;
	overflow: hidden;
}
 
.mission-vision-img img{
	aspect-ratio: 1 / 0.78;
    object-fit: cover;
	border-radius: 20px;
	filter: brightness(80%);
	transition: all 0.4s ease-in-out;
}

.mission-vision-item:hover .mission-vision-img img{
	transform: scale(1.1);
}

.mission-vision-image .icon-box{
	position: absolute;
	bottom: 20px;
	left: 20px;
}

.mission-vision-image .icon-box img{
	max-width: 40px;
}

.mission-vision-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 20px;
}

.mission-vision-content p{
	margin: 0;
}

.who-we-are{
	position: relative;
	padding: 80px 0;
}

.who-we-are::before{
    content: '';
    display: block;
    position: absolute;
    right: -90px;
    top: -25%;
    background: url(../images/agency-benefits-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 279px;
    height: 287px;
    animation: circlerotate 8s infinite linear;
    z-index: -1;
}

.who-we-are .who-we-are-content{
	padding-right: 90px;
}

.experts-rating-video{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}

.experts-rating-video-image{
	width: 50%;
	position: relative;
	border-radius: 20px;
	cursor: none;
    overflow: hidden;
	z-index: 1;
}

.experts-rating-video-image .video-image img{
	width: 100%;
	filter: brightness(80%);
	aspect-ratio: 1 / 0.71;
	object-fit: cover;
	border-radius: 20px;
	transition: all 0.5s ease-in-out;
}

.experts-rating-video-image:hover .video-image img{
    transform: scale(1.1);
}

.experts-rating-video-image .video-image a{
	cursor: none;
	position: relative;
	z-index: 1;
}

.experts-rating-video-image .video-play-button{
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 2;
    transform: translate(-50%, -50%);
}

.experts-rating-video-image .video-play-button a{
	position: relative;
	background-color: var(--accent-color);
	border-radius: 100%;
	width: 50px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: none;
}

.experts-rating-video-image .video-play-button a:before{
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 160%;
	height: 160%;
	border: 50px solid var(--primary-color);
	opacity: 30%;
	border-radius: 50%;
	transform: scale(0.6);
	z-index: -1;
	animation: border-zooming 1.2s infinite linear;
}

.experts-rating-video-image .video-play-button a:after{
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 160%;
	height: 160%;
	border: 50px solid var(--primary-color);
	opacity: 30%;
	border-radius: 50%;
	transform: scale(0.6);
	z-index: -1;
	animation: border-zooming 1.2s infinite linear;
	animation-delay: .3s;
}

@keyframes border-zooming{
	100%{
		transform: scale(1);
		opacity: 0;
	}
}

.experts-rating-video-image .video-play-button a i{
	font-size: 16px;
	color: var(--dark-color);
}

.who-we-are-client{
	width: 50%;
	padding-left: 50px;
}

.comapny-client-rating{
	margin-bottom: 20px;
}

.comapny-client-rating ul{
	list-style: none;
	margin: 0;
	padding: 0;
	margin-bottom: 5px;
}

.comapny-client-rating ul li i{
	font-size: 14px;
	color: var(--accent-color);
	margin-right: 2px;
}

.comapny-client-rating p{
	margin: 0;
}

.company-client-images{
	margin-bottom: 20px;
}

.company-client-images .client-image{
    display: inline-block;
    margin-left: -18px;
    border: 1px solid var(--secondary-color);
    border-radius: 50%;
    overflow: hidden;
}

.company-client-images .client-image:first-child{
    margin: 0;
}

.company-client-images .client-image figure{
	display: block;
}

.company-client-images .client-image img{
    max-width: 40px;
}

.contact-now-btn a{
	position: relative;
	color: var(--accent-color);
	font-size: 16px;
	font-weight: 700;
	padding-right: 30px;
	text-transform: capitalize;
}

.contact-now-btn a:hover{
	color: var(--primary-color);
}

.contact-now-btn a:after{
	content: '';
    position: absolute;
    top: -2px;
    right: 0;
    background-image: url(../images/arrow-accent.svg);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    width: 24px;
    height: 24px;
    transition: all 0.3s ease-in-out;
}

.contact-now-btn a:hover:after{
	filter: brightness(0) invert(1);
	transform: rotate(45deg);
}

.experts-counters-list{
	display: flex;
	flex-wrap: wrap;
	align-items: start;
	gap: 80px;
}

.experts-counter-box{
	width: calc(50% - 40px);
	display: flex;
	flex-wrap: wrap;
	gap: 80px;
}

.experts-counter-box:nth-child(even){
	margin-top: 60px;
}

.experts-counter-item{
	position: relative;
	width: 100%;
	background: url('../images/who-we-are-counter-bg.png');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
	border: 1px solid var(--divider-color);
	backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
	border-radius: 30px;
	padding: 30px;
	overflow: hidden;
}

.experts-counter-item:before{
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: var(--secondary-color);
	opacity: 40%;
	z-index: -1;
}

.experts-counter-box.expert-box-1 .experts-counter-item:nth-child(even){
	background: transparent;
}

.experts-counter-box.expert-box-2 .experts-counter-item:nth-child(odd){
	background: transparent;
}

.experts-counter-item .icon-box{
	text-align: right;
	margin-bottom: 20px;
}

.experts-counter-item .icon-box img{
	max-width: 30px;
	transition: all 0.3s ease-in-out;
}

.experts-counter-item:hover .icon-box img{
	filter: brightness(0) invert(1);
}

.experts-counter-content h2{
	font-size: 50px;
	font-weight: 800;
	color: var(--accent-color);
	margin-bottom: 10px;
}

.experts-counter-content p{
	margin: 0;
}

.executive-partners{
	padding: 80px 0;
}

.executive-partners-box{
	position: relative;
	border: 1px solid var(--divider-color);
	backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
	border-radius: 30px;
	padding: 70px;
	overflow: hidden;
}

.executive-partners-box::before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: var(--secondary-color);
    opacity: 40%;
    z-index: -1;
}

.executive-partners-box .section-title{
	margin-bottom: 0;
}

.our-partners-list{
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
}

.our-partners-list .company-logo{
	width: calc(33.33% - 20px);
	background: var(--dark-color);
	border-radius: 10px;
	text-align: center;
	padding: 25px 35px;
}

.our-partners-list .company-logo img{
	width: 100%;
	height: 30px;
}

.our-team{
	position: relative;
	padding: 80px 0 50px;
}

.our-team::before{
    content: '';
    display: block;
    position: absolute;
    left: -90px;
    top: -25%;
    background: url(../images/why-choose-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 310px;
    height: 325px;
    animation: circlezoomrotate 10s infinite linear;
    animation-direction: alternate;
    z-index: -1;
}

.team-item{
    position: relative;
    border-radius: 30px;
    height: calc(100% - 30px);
    margin-bottom: 30px;
    overflow: hidden;
}

.team-image a{
    position: relative;
    display: block;
}

.team-image a::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(181.78deg, rgba(0, 0, 0, 0) 47.87%, rgba(0, 0, 0, 0.8) 81.02%);
    z-index: 1;
}

.team-image figure, 
.team-image img{
    width: 100%;
    aspect-ratio: 1 / 1.38;
    object-fit: cover;
    display: block;
	transition: all 0.4s ease-in-out; 
}

.team-item:hover .team-image img{
	transform: scale(1.1);
}

.team-body{
    position: absolute;
    bottom: 30px;
    left: 30px;
    right: 30px;
    transform: translateY(40px);
    text-align: center;
    transition: all 0.4s ease-in-out;
    z-index: 2;
}

.team-item:hover .team-body{
    transform: translateY(0);
}

.team-content h3{
    font-size: 22px;
    margin-bottom: 5px;
    text-transform: capitalize;
}

.team-content h3 a{
    color: inherit;
}

.team-content p{
    text-transform: capitalize;
    margin-bottom: 0;
}

.team-social-list{
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.team-item:hover .team-social-list{
    margin-top: 15px;
    opacity: 1;
    visibility: visible;
}

.team-social-list ul{
    list-style: none;
    margin: 0;
    padding: 0;
}

.team-social-list ul li{
    display: inline-block;
    margin-right: 10px;
}

.team-social-list ul li:last-child{
    margin: 0;
}

.team-social-list ul li a{
    border: 1px solid var(--accent-color);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;
}

.team-social-list ul li:hover a{
    background-color: var(--accent-color);
}

.team-social-list ul li a i{
    color: var(--accent-color);
    font-size: 18px;
    transition: all 0.3s ease-in-out;
}

.team-social-list ul li:hover a i{
    color: var(--dark-color);
}

.our-faqs{
    padding: 80px 0;
}

.faq-images{
    position: relative;
    margin-right: 30px;
    padding: 140px 80px 0 0;
}

.faq-img-1{
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
}

.faq-img-1 figure{
    display: block;
    border-radius: 30px;
}

.faq-img-1 img{
    width: 100%;
    aspect-ratio: 1 / 1.02;
    object-fit: cover;
    border-radius: 30px;
}

.faq-img-2 figure{
    display: block;
    border-radius: 30px;
}

.faq-img-2 img{
	width: 100%;
    aspect-ratio: 1 / 1.06;
    object-fit: cover;
    border-radius: 30px;
}

.faq-cta-box{
    position: absolute;
    bottom: 0;
    right: 10%;
    transform: translate(0, -100%);
    animation: FaqCtaBoxMove 4s infinite linear;
}

@keyframes FaqCtaBoxMove{
	50%{
		right: 0;
	}
}

.faq-cta-box a{
    position: relative;
    display: block;
    color: var(--text-color);
    font-weight: 700;
    padding: 10px 18px;
    border: 1px solid var(--divider-color);
    border-radius: 10px;
    backdrop-filter: blur(60px);
    -webkit-backdrop-filter: blur(60px);
    overflow: hidden;
}

.faq-cta-box a::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    opacity: 16%;
    z-index: -1;
}

.faq-cta-box img{
    max-width: 30px;
    margin-right: 10px;
}

.our-faq-section .accordion-item{
    border: 1px solid var(--divider-color);
	border-radius: 10px;
	margin-bottom: 30px;
    padding: 0;
	transition: all 0.3s ease-in-out;
	overflow: hidden;
}

.our-faq-section .accordion-item:last-child{
	margin-bottom: 0;
}

.our-faq-section .accordion-header .accordion-button{
	font-size: 22px;
	font-weight: 700;
	line-height: 1.2em;
	background: transparent;
	color: var(--primary-color);
	padding: 17px 55px 17px 20px;
	transition: all 0.3s ease-in-out;
}

.our-faq-section .accordion-button:not(.collapsed){
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 50%, rgba(0, 0, 0, 0) 100%);
}

.our-faq-section .accordion-header .accordion-button.collapsed{
	color: var(--primary-color);
}

.our-faq-section .accordion-item .accordion-button::after,
.our-faq-section .accordion-item .accordion-button.collapsed::after{
	content: '\f068';
	font-family: "FontAwesome";
	position: absolute;
	right: 20px;
	top: 50%;
	bottom: auto;
	transform: translate(0px, -12px);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 400;
    line-height: normal;
	width: 24px;
	height: 24px;
    color: var(--dark-color);
	background-color: var(--primary-color);
    border-radius: 50%;
}

.our-faq-section .accordion-item .accordion-button.collapsed::after{
	content: '\2b';
	background-color: var(--accent-color);
}

.our-faq-section .accordion-item .accordion-body{
	background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 50%, rgba(0, 0, 0, 0) 100%);
	padding: 0 55px 18px 20px;
}

.our-faq-section .accordion-item .accordion-body p{
    color: var(--primary-color);
	margin: 0;
}

/************************************/
/***     19. Services Page css    ***/
/************************************/

.page-services{
	padding: 160px 0 50px;
}

.who-we-are.service-we-are::before{
	display: none;
}

/************************************/
/***    20. Services Single css   ***/
/************************************/

.page-service-single{
	position: relative;
    padding: 160px 0 80px;
}

.page-service-single::before{
    content: '';
    display: block;
    position: absolute;
    right: -120px;
    top: 15%;
    background: url(../images/about-agency-bg.png) no-repeat;
    background-position: right center;
    background-size: contain;
    opacity: 40%;
    width: 386px;
    height: 400px;
    animation: circlerotate 20s infinite linear;
    z-index: -1;
}

.page-service-single::after{
    content: '';
    display: block;
    position: absolute;
    left: -90px;
    top: 50%;
    background: url(../images/why-choose-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 310px;
    height: 325px;
    animation: circlezoomrotate 10s infinite linear;
    animation-direction: alternate;
    z-index: -1;
}

.service-single-content{
    margin-right: 30px;
}

.service-feature-image{
    margin-bottom: 40px;
}

.service-feature-image figure{
    display: block;
    border-radius: 30px;
}

.service-feature-image img{
    width: 100%;
    aspect-ratio: 1 / 0.67;
    object-fit: cover;
    border-radius: 30px;
}

.service-entry{
    margin-bottom: 40px;
}

.service-entry p{
    margin-bottom: 20px;
}

.service-entry p:last-child{
    margin-bottom: 0;
}

.service-entry h2{
    font-size: 50px;
    font-weight: 300;
    margin-bottom: 20px;
}

.service-entry h2 span{
    color: var(--accent-color);
    font-weight: 700;
}

.service-entry-list-image{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 40px;
    margin-bottom: 40px;
}

.service-entry-list{
    width: calc(55% - 20px);
}

.service-entry-list ul{
    margin: 0;
    padding: 0;
    list-style: none;
}

.service-entry-list ul li{
    position: relative;
    text-transform: capitalize;
    padding-left: 30px;
    margin-bottom: 24px;
}

.service-entry-list ul li:last-child{
    margin-bottom: 0;
}

.service-entry-list ul li::before{
    content: "\f192";
    font-family: "FontAwesome";
    position: absolute;
    top: 0;
    left: 0;
    font-size: 18px;
    color: var(--accent-color);
}

.service-entry-image{
    width: calc(45% - 20px);
}

.service-entry-image figure{
    display: block;
    border-radius: 30px;
    overflow: hidden;
}

.service-entry-image img{
    width: 100%;
    object-fit: cover;
    aspect-ratio: 1 / 0.89;
    border-radius: 30px;
}

.service-process-steps{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin: 30px 0 40px;
}

.process-step-item{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.process-step-item:nth-of-type(even){
    flex-direction: row-reverse;
}

.process-step-content{
    position: relative;
    background: url(../images/service-bg.svg) no-repeat;
    background-size: auto;
    background-position: top left;
    backdrop-filter: blur(100px);
    -webkit-backdrop-filter: blur(100px);
    width: calc(50% - 15px);
    padding: 40px;
    border: 1px solid var(--divider-color);
    border-radius: 30px;
}

.process-step-content::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
    border-radius: 30px;
}

.process-step-header{
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 70px;
}

.process-step-header .icon-box img{
    max-width: 60px;
}

.process-step-no h3{
    font-size: 22px;
    font-weight: 700;
    text-transform: uppercase;
    text-align: center;
}

.process-step-no h3 span{
    color: var(--accent-color);
    display: block;
}

.process-step-body{
    position: relative;
}

.process-step-body h3{
    font-size: 22px;
    font-weight: 700;
    text-transform: capitalize;
    margin-bottom: 20px;
}

.process-step-body p{
    margin-bottom: 0;
}

.process-step-image{
    width: calc(50% - 15px);
}

.process-step-image figure{
    display: block;
    height: 100%;
    border-radius: 30px;
    overflow: hidden;
}

.process-step-image img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 1 / 0.9;
    border-radius: 30px;
}

.service-sidebar{
    position: sticky;
    top: 20px;
}

.service-catagery-list{
    position: relative;
    border-radius: 30px;
    backdrop-filter: blur(100px);
    -webkit-backdrop-filter: blur(100px);
    margin-bottom: 60px;
	overflow: hidden;
}

.service-catagery-list::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
	z-index: -1;
}

.service-catagery-list h3{
    font-size: 22px;
    text-transform: capitalize;
    padding: 40px 40px 30px;
    border-bottom: 1px solid var(--divider-color);
}

.service-catagery-list ul{
    list-style: none;
    margin: 0;
    padding: 30px 40px 40px;
}

.service-catagery-list ul li{
    border-bottom: 1px solid var(--divider-color);
    padding-bottom: 30px;
    margin-bottom: 30px;
    transition: all 0.3s ease-in-out;
}

.service-catagery-list ul li:last-child{
    margin: 0;
    padding: 0;
    border-bottom: none;
}

.service-catagery-list ul li a{
    position: relative;
    display: block;
    text-transform: capitalize;
    color: var(--text-color);
    transition: all 0.3s ease-in-out;
}

.service-catagery-list ul li:hover a{
    color: var(--accent-color);
}

.service-catagery-list ul li a::before{
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    background: url(../images/arrow-accent.svg) no-repeat;
    background-position: right center;
    background-size: cover;
    width: 26px;
    height: 26px;
    transition: all 0.3s ease-in-out;
}

.service-catagery-list ul li:hover a::before{
    transform: rotate(45deg);
}

.sidebar-cta-box{
    position: relative;
    background: url(../images/sidebar-cta-bg.svg) no-repeat;
    background-position: center center;
    background-size: cover;
    padding: 50px;
    backdrop-filter: blur(100px);
    -webkit-backdrop-filter: blur(100px);
    border: 1px solid var(--divider-color);
    border-radius: 30px;
    text-align: center;
    overflow: hidden;
}

.sidebar-cta-box::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
	z-index: -1;
}

.sidebar-cta-box .icon-box{
    position: relative;
    margin-bottom: 40px;
}

.sidebar-cta-box .icon-box img{
    max-width: 60px;
}

.cta-contact-content{
    position: relative;
    margin-bottom: 20px;
}

.cta-contact-content h3{
    font-size: 22px;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 20px;
}

.cta-contact-content p{
    margin-bottom: 0;
}

.cta-contact-btn{
    position: relative;
}

.cta-contact-btn a{
    display: inline-block;
    font-weight: 700;
    color: var(--dark-color);
    backdrop-filter: blur(60px);
    -webkit-backdrop-filter: blur(60px);
    background-color: var(--accent-color);
    border-radius: 10px;
    padding: 12px 35px;
    transition: all 0.4S ease-in-out;
}

.cta-contact-btn a:hover{
    background-color: var(--dark-divider-color);
    color: var(--primary-color);
}

.cta-contact-btn a img{
    margin-right: 12px;
    max-width: 30px;
    transition: all 0.4S ease-in-out;
}

.cta-contact-btn a:hover img{
    filter: brightness(1) invert(1);
}

/************************************/
/*** 	 21. Blog Archive Css	  ***/
/************************************/

.page-blog{
	position: relative;
    padding: 160px 0 80px;
}

.page-blog::before{
    content: '';
    display: block;
    position: absolute;
    right: -120px;
    top: 40%;
    background: url(../images/about-agency-bg.png) no-repeat;
    background-position: right center;
    background-size: contain;
    opacity: 40%;
    width: 386px;
    height: 400px;
    animation: circlerotate 20s infinite linear;
    z-index: -1;
}

.post-item{
    height: calc(100% - 40px);
    margin-bottom: 40px;
}

.page-pagination{
    margin-top: 20px;
    text-align: center;
}

.page-pagination ul{
    justify-content: center;
    padding: 0;
    margin: 0;
}

.page-pagination ul li a,
.page-pagination ul li span{
    display: flex;
    text-decoration: none;
    justify-content: center;
    align-items: center;
    background: var(--secondary-color);
    color: var(--primary-color);
	border-radius: 10px;
    width: 40px;
    height: 40px;
    margin: 0 5px;
    font-weight: 700;
	line-height: 1em;
    transition: all 0.3s ease-in-out;
}

.page-pagination ul li.active a, 
.page-pagination ul li a:hover{
    background: var(--accent-color);
	color: var(--dark-color);
}

/************************************/
/***      22. Blog Single css	  ***/
/************************************/

.page-single-post{
	padding: 160px 0 80px;
}

.post-image{
	position: relative;
	margin-bottom: 30px;
}

.post-image figure{
	display: block;
}

.post-image figure,
.post-image img{
	aspect-ratio: 1 / 0.50;
	object-fit: cover;
	border-radius: 30px;
}

.post-content{
	width: 100%;
	max-width: 1100px;
	margin: 0 auto;
}

.post-entry{
	border-bottom: 1px solid var(--divider-color);
	padding-bottom: 30px;
    margin-bottom: 30px;
}

.post-entry:after{
    content: '';
    display: block;
    clear: both;
}

.post-entry a{
    color: var(--accent-color);
}

.post-entry h1,
.post-entry h2,
.post-entry h3,
.post-entry h4,
.post-entry h5,
.post-entry h6{
	font-weight: 300;
	line-height: 1.2em;
	margin: 0 0 0.6em;
}

.post-entry h1 span,
.post-entry h2 span{
	font-weight: 800;
}

.post-entry h1{
	font-size: 80px;
}

.post-entry h2{
	font-size: 50px;
}

.post-entry h3{
	font-size: 40px;
}

.post-entry h4{
	font-size: 30px;
}

.post-entry h5{
	font-size: 24px;
}

.post-entry h6{
	font-size: 18px;
}

.post-entry p{
	margin-bottom: 20px;
}

.post-entry p:last-child{
	margin-bottom: 0;
}

.post-entry p strong{
	color: var(--primary-color);
	font-size: 18px;
	font-weight: 600;
}

.post-entry ol{
    margin: 0 0 30px;
}

.post-entry ol li{
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.post-entry ul{
	padding: 0;
	margin: 20px 0 20px;
	padding-left: 20px;
}

.post-entry ul li{
	font-size: 18px;
    font-weight: 500;
    color: var(--primary-color);
    position: relative;
    margin-bottom: 15px;
}

.post-entry ul li:last-child{
	margin-bottom: 0;
}

.post-entry ul ul,
.post-entry ul ol,
.post-entry ol ol,
.post-entry ol ul{
    margin-top: 20px;
    margin-bottom: 0;
}

.post-entry ul ul li:last-child,
.post-entry ul ol li:last-child,
.post-entry ol ol li:last-child,
.post-entry ol ul li:last-child{
    margin-bottom: 0;
}

.post-entry blockquote{
	position: relative;
	background: url(../images/icon-blockquote.svg) no-repeat;
	border: 1px solid var(--divider-color);
	backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
	background-position: 35px 30px;
    background-size: 58px;
    border-radius: 20px;
    padding: 30px 30px 30px 100px;
    margin-bottom: 30px;
	overflow: hidden;
}

.post-entry blockquote::before{
	content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
	z-index: -1;
}

.post-entry blockquote p{
	font-size: 20px;
	font-weight: 600;
	line-height: 1.4em;
	color: var(--primary-color);
}

.post-entry blockquote p:last-child{
	margin-bottom: 0;
}

.tag-links{
	font-size: 24px;
	font-weight: 600;
	color: var(--primary-color);
	display: inline-flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 10px;
}

.post-tags .tag-links a{
    display: inline-block;
    font-size: 16px;
    font-weight: 600;
    text-transform: capitalize;
    background: var(--accent-color);
    background-size: 200% auto;
    color: var(--dark-color);
	border-radius: 100px;
    padding: 8px 20px;
	transition: all 0.3s ease-in-out;
}

.post-tags .tag-links a:hover{
	background: var(--primary-color);
}

.post-social-sharing{
    text-align: right;
}

.post-social-sharing ul{
    list-style: none;
    padding: 0;
    margin: 0;
}

.post-social-sharing ul li{
    display: inline-block;
    margin-right: 10px;
}

.post-social-sharing ul li:last-child{
	margin-right: 0;
}

.post-social-sharing ul li a{
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
	background: var(--accent-color);
    background-size: 200% auto;
    color: var(--dark-color);
	border-radius: 50%;
    width: 36px;
    height: 36px;
    transition: all 0.3s ease-in-out;
}

.post-social-sharing ul li:hover a{
	background: var(--primary-color);
}

.post-social-sharing ul li a i{
    font-size: 18px;
    color: inherit;
}

/************************************/
/***      23. Project Page css	  ***/
/************************************/

.page-project{
	position: relative;
	padding: 160px 0 40px;
}

.page-project::before{
    content: '';
    display: block;
    position: absolute;
    right: -120px;
    top: 40%;
    background: url(../images/about-agency-bg.png) no-repeat;
    background-position: right center;
    background-size: contain;
    opacity: 40%;
    width: 386px;
    height: 400px;
    animation: circlerotate 20s infinite linear;
    z-index: -1;
}

/************************************/
/***     24. Project Single css	  ***/
/************************************/

.page-project-single{
	position: relative;
    padding: 160px 0 80px;
}

.page-project-single::before{
    content: '';
    display: block;
    position: absolute;
    left: -90px;
    top: 10%;
    background: url(../images/agency-benefits-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 279px;
    height: 287px;
    animation: circlerotate 8s infinite linear;
    z-index: -1;
}

.page-project-single::after{
    content: '';
    display: block;
    position: absolute;
    right: -90px;
    top: 60%;
    background: url(../images/why-choose-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 310px;
    height: 325px;
    animation: circlezoomrotate 10s infinite linear;
    animation-direction: alternate;
    z-index: -1;
}

.project-single-content{
    margin-right: 30px;
}

.project-single-image{
    margin-bottom: 40px;
}

.project-single-image figure{
    display: block;
    border-radius: 30px;
}

.project-single-image img{
    width: 100%;
    aspect-ratio: 1 / 0.67;
    object-fit: cover;
    border-radius: 30px;
}

.project-challenges,
.project-info,
.project-entry{
    margin-bottom: 60px;
}

.project-entry p{
    margin-bottom: 20px;
}

.project-entry p:last-child{
    margin-bottom: 0;
}

.project-entry h2{
    font-size: 50px;
    font-weight: 300;
    color: var(--primary-color);
    margin-bottom: 30px;
}

.project-entry h2 span{
    color: var(--accent-color);
    font-weight: 700;
}

.project-entry ul{
    margin: 0;
    padding: 0;
    list-style: none;
}

.project-entry ul li{
    position: relative;
    text-transform: capitalize;
    padding-left: 30px;
    margin-bottom: 24px;
}

.project-entry ul li:last-child{
    margin-bottom: 0;
}

.project-entry ul li::before{
    content: "\f192";
    font-family: "FontAwesome";
    position: absolute;
    top: 0;
    left: 0;
    font-size: 18px;
    color: var(--accent-color);
}

.project-solution-rating{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 60px;
    margin-top: 30px;
}

.project-rating-content{
    width: calc(70% - 30px);
}

.project-rating-counter{
    width: calc(30% - 30px);
    display: flex;
    align-items: center;
}

.project-rating-counter .icon-box{
    margin-right: 20px;
}

.project-rating-counter .icon-box i{
    font-size: 48px;
    color: var(--accent-color);
}

.project-counter-content{
    width: calc(100% - 68px);
}

.project-counter-content h3{
    font-size: 40px;
}

.project-sidebar{
	position: sticky;
	top: 20px;
}

.project-catagery-list{
	position: relative;
    border: 1px solid var(--divider-color);
	backdrop-filter: blur(100px);
	-webkit-backdrop-filter: blur(100px);
    border-radius: 30px;
    margin-bottom: 60px;
	overflow: hidden;
}

.project-catagery-list::before{
    content: '';
	display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
	z-index: -1;
}

.category-item-list{
    padding: 40px 40px 30px;
}

.category-list-item{
    border-bottom: 1px solid var(--divider-color);
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.category-list-item:last-child{
    margin: 0;
    padding: 0;
    border-bottom: none;
}

.category-list-item h3{
    font-size: 22px;
    text-transform: capitalize;
    margin-bottom: 10px;
}

.category-list-item p{
    text-transform: capitalize;
    margin-bottom: 0;
}

.category-social-link{
    border-top: 1px solid var(--divider-color);
	display: flex;
    align-items: center;
    padding: 30px 40px 40px;
}

.category-social-link span{
    font-size: 22px;
    font-weight: 700;
	margin-right: 15px;
}

.category-social-link ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

.category-social-link ul li{
	display: inline-block;
	margin-right: 5px;
}

.category-social-link ul li:last-child{
	margin-right: 0;
}

.category-social-link ul li a{
    border: 1px solid var(--accent-color);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;
}

.category-social-link ul li a:hover{
    border-color: var(--primary-color);
}

.category-social-link ul li a i{
    color: var(--accent-color);
    font-size: 18px;
    transition: all 0.3s ease-in-out;
}

.category-social-link ul li a:hover i{
    color: var(--primary-color);
}

/************************************/
/***      25. Team Page css	      ***/
/************************************/

.page-team{
	position: relative;
	padding: 160px 0 50px;
}

.page-team::before{
    content: '';
    display: block;
    position: absolute;
    right: -120px;
    top: 50%;
    background: url(../images/work-together-bg-shape.png) no-repeat;
    background-position: right center;
    background-size: contain;
    opacity: 40%;
    width: 305px;
    height: 315px;
    animation: circlmoveerotate 10s infinite linear;
    animation-direction: alternate;
    z-index: -1;
}

/************************************/
/***      26. Team Single css	  ***/
/************************************/

.page-team-single{
	position: relative;
    padding: 160px 0 80px;
}

.page-team-single::before{
    content: '';
    display: block;
    position: absolute;
    right: -90px;
    top: 60%;
    background: url(../images/why-choose-bg-shape.png) no-repeat;
    background-position: left center;
    background-size: contain;
    opacity: 50%;
    width: 310px;
    height: 325px;
    animation: circlezoomrotate 10s infinite linear;
    animation-direction: alternate;
    z-index: -1;
}

.team-single-content{
    margin-right: 30px;
}

.team-single-content h2{
    font-size: 50px;
    font-weight: 300;
    margin-bottom: 20px;
}

.team-single-content h2 span{
    color: var(--accent-color);
    font-weight: 700;
}

.team-single-content p{
    margin-bottom: 20px;
}

.team-single-content p:last-child{
    margin-bottom: 0;
}

.team-info-box{
    margin-bottom: 60px;
}

.team-info-header{
	display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    gap: 30px;
    margin-bottom: 40px;
}

.team-info-title{
    width: calc(75% - 15px);
}

.team-info-title p{
    color: var(--accent-color);
    text-transform: capitalize;
    margin-bottom: 5px;
}

.team-info-title h2{
    font-weight: 700;
    text-transform: capitalize;
    margin: 0;
}

.team-info-social-list{
    width: calc(25% - 15px);
}

.team-info-social-list ul{
    margin: 0;
    padding: 0;
    list-style: none;
	text-align: end;
}

.team-info-social-list ul li{
    display: inline-block;
    margin-right: 10px;
}

.team-info-social-list ul li:last-child{
    margin: 0;
}

.team-info-social-list ul li a{
    border: 1px solid var(--accent-color);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;
}

.team-info-social-list ul li:hover a{
    background-color: var(--accent-color);
}

.team-info-social-list ul li a i{
    font-size: 18px;
    color: var(--accent-color);
    transition: all 0.3s ease-in-out;
}

.team-info-social-list ul li:hover a i{
    color: var(--dark-color);
}

.team-contact-box{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    border-bottom: 1px solid var(--divider-color);
    padding-bottom: 30px;
    margin-bottom: 30px;
}

.team-contact-box:last-child{
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.team-contact-box .icon-box{
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    margin-right: 30px;
    border: 1px solid var(--dark-divider-color);
    border-radius: 50%;
    backdrop-filter: blur(100px);
    -webkit-backdrop-filter: blur(100px);
	overflow: hidden;
}

.team-contact-box .icon-box::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 30%;
	z-index: -1;
}

.team-contact-box .icon-box img{
    max-width: 36px;
}

.team-contact-content{
    width: calc(100% - 110px);
}

.team-contact-content h3{
    font-size: 22px;
    font-weight: 600;
    text-transform: capitalize;
    margin-bottom: 10px;
}

.team-contact-content p{
	margin: 0;
}

.team-personal-info{
    margin-bottom: 60px;
}

.team-career-list ul{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.team-career-list ul li{
    position: relative;
    width: calc(50% - 15px);
    text-transform: capitalize;
    padding-left: 30px;
}

.team-career-list ul li::before{
    content: "\f192";
    font-family: "FontAwesome";
    position: absolute;
    top: 0;
    left: 0;
    font-size: 18px;
    color: var(--accent-color);
}

.team-single-sidebar{
    position: sticky;
    top: 30px;
}

.team-single-image{
    margin-bottom: 60px;
}

.team-single-image figure{
    display: block;
    border-radius: 30px;
    overflow: hidden;
	visibility: visible;
}

.team-single-image img{
    width: 100%;
    aspect-ratio: 1 / 1.22;
    object-fit: cover;
    border-radius: 30px
}

.team-single-contact-form{
    position: relative;
    border-radius: 30px;
    border: 1px solid var(--divider-color);
    backdrop-filter: blur(100px);
    -webkit-backdrop-filter: blur(100px);
	overflow: hidden;
}

.team-single-contact-form::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
    z-index: -1;
}

.team-single-contact-form h3{
    position: relative;
    font-size: 34px;
    font-weight: 300;
    padding: 40px 40px 30px;
    border-bottom: 1px solid var(--divider-color);
}

.team-single-contact-form h3 span{
    font-weight: 700;
    color: var(--accent-color);
}

.team-single-contact-form form{
    position: relative;
    padding: 40px;
}

.team-single-contact-form form .form-control{
    font-size: 16px;
    font-weight: 400;
    color: var(--text-color);
    padding: 16px 20px;
    background-color: transparent;
    border: 1px solid var(--divider-color);
    border-radius: 10px;
    outline: none;
    box-shadow: none;
}

.team-single-contact-form form .form-control::placeholder{
    color: var(--text-color);
}

.contact-form-btn .btn-highlighted{
	width: 100%;
	text-align: center;
}

/************************************/
/***      27. Pricing Page css	  ***/
/************************************/

.page-pricing{
    padding: 160px 0 80px;
}

.pricing-box-list{
    display: flex;
    flex-wrap: wrap;
    gap: 40px 60px;
}

.pricing-box{
    position: relative;
    background: url(../images/service-bg.svg) no-repeat;
    background-position: top left;
    background-size: auto;
    width: calc(50% - 30px);
    border: 1px solid var(--divider-color);
    border-radius: 30px;
    padding: 50px;
    backdrop-filter: blur(100px);
    -webkit-backdrop-filter: blur(100px);
	overflow: hidden;
}

.pricing-box::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
    z-index: -1;
}

.pricing-box-content{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 30px;
    border-bottom: 1px solid var(--divider-color);
    margin-bottom: 40px;
    padding-bottom: 40px;
}

.pricing-title{
    width: calc(60% - 15px);
}

.pricing-title h3{
    font-size: 30px;
    text-transform: capitalize;
    margin-bottom: 10px; 
}

.pricing-title p{
    margin-bottom: 0;
}

.pricing-price{
    width: calc(40% - 15px);
    text-align: end;
}

.pricing-price h2{
    font-size: 50px;
    color: var(--accent-color);
}

.pricing-price h2 sup{
    font-size: 22px;
    color: var(--primary-color);
}

.pricing-price h2 sub{
    bottom: 0;
    font-size: 16px;
    font-weight: 400;
    color: var(--primary-color);
}

.pricing-list{
    margin-bottom: 40px;
}

.pricing-list ul{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.pricing-list ul li{
    position: relative;
    width: calc(50% - 15px);
    text-transform: capitalize;
    padding-left: 30px;
}

.pricing-list ul li::before{
    content: "\f192";
    font-family: "FontAwesome";
    position: absolute;
    top: 0;
    left: 0;
    font-size: 18px;
    color: var(--accent-color);
}

.pricing-btn .btn-highlighted{
	width: 100%;
	text-align: center;
}

/************************************/
/***    28. Testimonial Page css  ***/
/************************************/

.page-testimonial{
    padding: 160px 0 80px;
}

.testimonial-box-list{
    display: flex;
    flex-wrap: wrap;
    gap: 60px;
}

.testimonial-box-item{
    position: relative;
    display: flex;
	align-items: center;
    flex-wrap: wrap;
    gap: 50px;
    width: 100%;
    border: 1px solid var(--divider-color);
    border-radius: 30px;
    backdrop-filter: blur(100px);
    -webkit-backdrop-filter: blur(100px);
    padding: 80px;
	overflow: hidden;
}

.testimonial-box-item::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
    z-index: -1;
}

.client-author-image{
    width: calc(27% - 25px);
}

.client-author-image figure{
    display: block;
    border-radius: 50%;
    overflow: hidden;
}

.client-author-image img{
    width: 100%;
    object-fit: cover;
    aspect-ratio: 1 / 1;
    border-radius: 50%;
}

.client-testimonial-content{
    width: calc(73% - 25px);
}

.client-testimonial-rating{
    margin-bottom: 20px;
}

.client-testimonial-rating ul{
    margin: 0;
    padding: 0;
    list-style: none;
}

.client-testimonial-rating ul li{
    display: inline-block;
}

.client-testimonial-rating ul li i{
    font-size: 12px;
	color: var(--accent-color);
    margin-right: 2px;
}

.client-testimonial-rating ul li:last-child i{
    margin: 0;
}

.client-testimonial-info{
    margin-bottom: 20px;
}

.client-testimonial-info p{
	font-size: 20px;
}

.client-testimonial-info p:last-child{
    margin: 0;
}

.client-author-content{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    align-items: center;
}

.client-author-title{
    width: calc(65% - 15px);
}

.client-author-title h3{
    font-size: 22px;
    text-transform: capitalize;
    margin-bottom: 5px;
}

.client-author-title p{
    text-transform: capitalize;
    margin-bottom: 0;
}

.client-author-logo{
    width: calc(35% - 15px);
    text-align: end;
}

.client-author-logo img{
	max-width: 125px;
	max-height: 30px;
}

/************************************/
/***     27. Image Gallery css    ***/
/************************************/

.page-gallery{
	padding: 160px 0 50px;
}

.page-gallery-box .photo-gallery{
	height: calc(100% - 30px);
	margin-bottom: 30px;
}

.page-gallery-box .photo-gallery a{
	cursor: none;
}

.page-gallery-box .photo-gallery figure{
	border-radius: 20px;
}

.page-gallery-box .photo-gallery img{
	aspect-ratio: 1 / 0.8;
	object-fit: cover;
	border-radius: 20px;
}

/************************************/
/***     30. Video Gallery css    ***/
/************************************/

.page-video-gallery{
	padding: 160px 0 50px;
}

.video-gallery-image{
	border-radius: 20px;
	overflow: hidden;
	height: calc(100% - 30px);
	margin-bottom: 30px;
}

.video-gallery-image a{
	position: relative;
	display: block;
	cursor: none;
}

.video-gallery-image a::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--secondary-color);
    border-radius: 20px;
    opacity: 0%;
    visibility: hidden;
    width: 100%;
    height: 100%;
    z-index: 1;
    transform: scale(0);
    transition: all 0.4s ease-in-out;
}

.video-gallery-image:hover a::before{
    opacity: 50%;
    visibility: visible;
    transform: scale(1);
}

.video-gallery-image a::after{
    content: '\f04b';
	font-family: 'FontAwesome';
    position: absolute;
    top: 50%;
    left: 50%;
    right: 0;
    transform: translate(-50%, -50%);
	font-size: 20px;
	background: var(--accent-color);
	color: var(--dark-color);
    border-radius: 50%;
    height: 60px;
    width: 60px;
    cursor: none;
	display: flex;
	align-items: center;
	justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease-in-out;
    z-index: 1;
}

.video-gallery-image:hover a::after{
    opacity: 1;
    visibility: visible;
}

.video-gallery-image img{
	aspect-ratio: 1 / 0.8;
	object-fit: cover;
	border-radius: 20px;
}

/************************************/
/***       31. FAQs Page css      ***/
/************************************/

.page-faqs{
    padding: 160px 0 80px;
}

.page-faqs-catagery{
    margin-right: 30px;
}

.page-faq-accordion{
    margin-bottom: 80px;
}

.page-faq-accordion:last-child{
    margin-bottom: 0;
}

.faq-sidebar{
    position: sticky;
    top: 20px;
}

.faq-catagery-list{
    position: relative;
    border-radius: 30px;
    backdrop-filter: blur(100px);
    -webkit-backdrop-filter: blur(100px);
    margin-bottom: 60px;
	overflow: hidden;
}

.faq-catagery-list::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
	z-index: -1;
}

.faq-catagery-list ul{
    list-style: none;
    margin: 0;
    padding: 40px;
}

.faq-catagery-list ul li{
    border-bottom: 1px solid var(--divider-color);
    padding-bottom: 30px;
    margin-bottom: 30px;
    transition: all 0.3s ease-in-out;
}

.faq-catagery-list ul li:last-child{
    margin: 0;
    padding: 0;
    border-bottom: none;
}

.faq-catagery-list ul li a{
    position: relative;
    display: block;
    text-transform: capitalize;
    color: var(--text-color);
    transition: all 0.3s ease-in-out;
}

.faq-catagery-list ul li:hover a{
    color: var(--accent-color);
}

.faq-catagery-list ul li a::before{
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    background: url(../images/arrow-accent.svg) no-repeat;
    background-position: right center;
    background-size: cover;
    width: 26px;
    height: 26px;
    transition: all 0.3s ease-in-out;
}

.faq-catagery-list ul li:hover a::before{
    transform: rotate(45deg);
}

/************************************/
/***    32. Contact Us Page css   ***/
/************************************/

.page-contact-us{
    padding: 160px 0 80px;
}

.contact-info-box{
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
}

.contact-info-box .info-box-1{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.contact-info-box .info-box-1,
.contact-info-box .info-box-2{
    position: relative;
    width: 100%;
	border: 1px solid var(--divider-color);
	border-radius: 30px;
    padding: 30px 40px;
	overflow: hidden;
}

.contact-info-box .info-box-1::before,
.contact-info-box .info-box-2::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 30%;
    z-index: -1;
}

.contact-info-item{
    text-align: center;
    width: 100%;
}

.contact-info-box .info-box-1 .contact-info-item{
    position: relative;
    width: calc(50% - 15px);
}

.contact-info-box .info-box-1 .contact-info-item::before{
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(15px, -50%);
    height: 80%;
    width: 1px;
    background-color: var(--divider-color);
}

.contact-info-box .info-box-1 .contact-info-item:nth-child(2n + 2):before{
	display: none;
}

.contact-info-box .info-box-2 .contact-info-item{
    display: flex;
    text-align: left;
}

.contact-info-item .icon-box{
    margin-bottom: 30px;
}

.contact-info-box .info-box-2 .icon-box{
    margin: 0 30px 0 0;
}

.contact-info-item .icon-box img{
    max-width: 50px;
}

.contact-info-box .info-box-2 .contact-item-content{
    width: calc(100% - 80px);
}

.contact-item-content h3{
    font-size: 22px;
    font-weight: 600;
    text-transform: capitalize;
    margin-bottom: 10px;
}

.contact-item-content p{
    margin: 0;
}

.contact-us-form{
    position: relative;
    padding: 50px;
    border: 1px solid var(--divider-color);
    border-radius: 30px;
    margin-left: 30px;
}

.contact-us-form::before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    opacity: 40%;
    border-radius: 30px;
    z-index: -1;
}

.contact-us-form form .form-control{
    font-size: 16px;
    font-weight: 400;
    color: var(--text-color);
    padding: 16px 20px;
    background-color: transparent;
    border: 1px solid var(--divider-color);
    border-radius: 10px;
    outline: none;
    box-shadow: none;
}

.contact-us-form form .form-control::placeholder{
    color: var(--text-color);
}

.google-map{
	padding: 80px 0;
}

.google-map-iframe,
.google-map-iframe iframe{
    width: 100%;
    height: 600px;
    border-radius: 30px;
}

.google-map-iframe iframe{
    filter: grayscale(1);
    transition: all 0.4s ease-in-out;
}

.google-map-iframe iframe:hover{
    filter: grayscale(0);
}

/************************************/
/*** 	   33. 404 Page css       ***/
/************************************/

.error-page{
	padding: 160px 0 80px;
}

.error-page-image{
	text-align: center;
	margin-bottom: 30px;
}

.error-page-image img{
	width: 100%;
	max-width: 50%;
}

.error-page .error-page-content{
	text-align: center;
}

.error-page-content-heading{
	margin-bottom: 30px;
}

.error-page-content-body p{
	margin-bottom: 30px;
}

/************************************/
/***      34. Responsive css      ***/
/************************************/

@media only screen and (max-width: 991px){

	.navbar{
		padding: 20px 0;
	}

	.slicknav_nav li,
	.slicknav_nav ul{
        display: block;
    }

	.responsive-menu,
    .navbar-toggle{
        display: block;
    }

	.header-social-links{
		display: none;
	}

	.section-row{
		margin-bottom: 40px;
	}

	.section-row .section-title{
		max-width: 100%;
	}

	.section-title{
		margin-bottom: 30px;
	}

	.section-title h3{
		margin-bottom: 15px;
	}

	.section-title h3::before{
		width: 14px;
		height: 14px;
	}

	.section-title h1{
		font-size: 70px;
	}

	.section-title h2{
		font-size: 40px;
	}

	.section-title p{
		margin-top: 15px;
	}

	.section-content-btn .section-title-content{
		margin-bottom: 20px;
	}

	.section-title-content{
		margin-top: 15px;
	}

	.hero{
		padding: 180px 0 90px;
		min-height: auto;
	}

	.hero.hero-slider-layout .hero-slide{
		padding: 180px 0 90px;
		min-height: auto;
	}

	.hero-content .section-title{
		margin-bottom: 40px;
	}

	.hero-content-body{
		margin-left: 0px;
	}

	.hero-content-video{
		width: 37%;
	}

	.hero-video-content{
		width: 63%;
	}

	.hero-btn{
		margin-top: 30px;
	}

	.our-scrolling-ticker{
		padding: 22px 0;
	}

	.scrolling-content span{
		font-size: 34px;
	}

	.about-agency{
		padding: 80px 0 40px;
	}

	.about-agency::before{
		width: 300px;
		height: 330px;
	}

	.about-agency-content{
		position: initial;
		padding-right: 0px;
		margin-bottom: 30px;
	}

	.about-agency-list{
		gap: 30px;
		padding-left: 30px;
		margin-left: 10px;
	}

	.agency-item-content h3:after{
		left: -40px;
	}

	.agency-item-content h3{
		font-size: 20px;
	}

	.our-services{
		padding: 40px 0;
	}

	.service-item{
		padding: 30px;
	}

	.service-item-header{
		margin-bottom: 50px;
	}

	.service-item-header .icon-box img{
		max-width: 50px;
	}

	.service-item-body h3{
		font-size: 20px;
		margin-bottom: 15px;
	}

	.service-footer{
		margin-top: 10px;
	}

	.digital-success{
		padding: 40px 0;
	}

	.digital-success-box{
		gap: 40px;
		padding: 50px;
	}

	.digital-success-list,
	.digital-success-content{
		width: 100%;
	}

	.digital-success-content::before{
		display: none;
	}

	.digital-success-content .section-title{
		margin-bottom: 40px;
	}

	.success-counter-item h2{
		font-size: 40px;
	}

	.success-list-item{
		margin-bottom: 40px;
	}

	.success-list-item p span{
		font-size: 20px;
	}

	.why-choose-us{
		padding: 40px 0;
	}

	.why-choose-us::before{
		top: 20%;
		width: 250px;
		height: 280px;
	}

	.why-choose-content{
		margin-right: 0;
		margin-bottom: 30px;
	}

	.why-choose-item{
		padding: 15px 20px;
	}

	.why-choose-item h3{
		font-size: 20px;
	}

	.why-choose-image img{
		aspect-ratio: 1 / 0.73;
	}

	.join-agency{
		padding: 40px 0 10px;
	}

	.agency-social-item{
		padding: 20px 30px 20px 50px;
		margin: 20px 0 30px 20px;
	}

	.agency-social-item .icon-box{
		top: -20px;
		left: -20px;
	}

	.agency-social-item .icon-box a{
		width: 60px;
		height: 60px;
	}

	.agency-social-item .icon-box i{
		font-size: 30px;
	}

	.agency-social-content h3{
		font-size: 20px;
	}

	.how-it-work{
		padding: 40px 0 10px;
	}

	.how-it-work::before{
		top: 0;
		width: 240px;
		height: 280px;
	}

	.work-process-item{
		padding: 30px;
	}

	.work-process-header{
		margin-bottom: 30px;
	}

	.work-process-title h3{
		font-size: 20px;
	}

	.work-process-content{
		margin-bottom: 40px;
	}

	.work-process-body{
		padding-top: 10px;
	}

	.work-process-no h3{
		font-size: 20px;
	}

	.work-process-no h2{
		font-size: 34px;
	}

	.work-process-icon-box{
		padding: 30px;
	}

	.work-process-icon-box img{
		max-width: 50px;
	}

	.our-features{
		padding: 40px 0;
	}

	.our-features::before{
		top: 15%;
		width: 250px;
		height: 225px;
	}

	.digital-features-box{
		gap: 30px;
	}

	.digital-features-item.features-item-1{
		width: calc(55% - 15px);
	}

	.digital-features-item.features-item-2{
		width: calc(45% - 15px);
	}

	.digital-features-item{
		padding: 30px;
	}

	.digital-features-content h3{
		font-size: 20px;
	}

	.digital-features-image{
		margin-bottom: 30px;
	}

	.digital-features-item.features-item-1 .digital-features-image img{
		aspect-ratio: 1 / 0.62;
	}

	.digital-features-item.agency-supports{
		padding: 30px 0;
	}

	.agency-supports-header{
		margin-bottom: 30px;
		padding: 0 30px;
	}

	.agency-supports-content{
		width: 70%;
	}

	.agency-supports-content h3{
		font-size: 20px;
	}

	.agency-free-consultation{
		width: 30%;
	}

	.agency-free-consultation img{
		max-width: 80px;
	}

	.agency-supports-logo{
		padding: 20px 25px;
	}

	.agency-supports-logo img{
		max-height: 25px;
	}

	.our-portfolio{
		padding: 40px 0 10px;
	}

	.our-Project-nav{
		margin-bottom: 40px;
	}

	.our-Project-nav ul{
		gap: 10px 20px;
	}

	.our-Project-nav ul li a{
		font-size: 14px;
		padding: 10px 15px;
	}

	.project-item{
		height: calc(100% - 30px);
		margin-bottom: 30px;
	}

	.project-btn a{
		width: 80px;
		height: 80px;
	}

	.project-btn a img{
		max-width: 34px;
	}

	.project-content h3{
		font-size: 20px;
	}

	.our-testimonial{
		padding: 40px 0;
	}

	.our-testimonial::before{
		width: 250px;
		height: 245px;
	}

	.testimonial-review-box{
		padding: 30px;
		margin-bottom: 30px;
	}

	.testimonial-review-content,
	.testimonial-review-header{
		margin-bottom: 20px;
	}

	.testimonial-review-header h2{
		font-size: 60px;
	}

	.testimonial-review-content h3{
		font-size: 20px;
	}

	.testimonial-slider{
		margin-left: 0px;
	}

	.testimonial-company-logo{
		margin-bottom: 20px;
	}

	.testimonial-content{
		margin-bottom: 30px;
	}

	.testimonial-content p{
		font-size: 20px;
	}

	.author-content h3{
		font-size: 20px;
	}

	.testimonial-slider .testimonial-button-next,
	.testimonial-slider .testimonial-button-prev{
		width: 50px;
		height: 50px;
	}

	.testimonial-slider .testimonial-button-next::before,
	.testimonial-slider .testimonial-button-prev::before{
		background-size: 24px auto;
	}

	.testimonial-benefits-box{
		gap: 20px;
		margin-top: 50px;
		padding: 30px 15px;
	}

	.testimonial-benefits-item{
		width: calc(25% - 15px);
	}

	.testimonial-benefits-item::before{
		right: -10px;
	}

	.testimonial-benefits-item .icon-box{
		margin-bottom: 20px;
	}

	.testimonial-benefits-content h3{
		font-size: 20px;
		margin-bottom: 15px;
	}

	.testimonial-benefits-content ul li{
		font-size: 14px;
		padding-left: 18px;
		margin-bottom: 10px;
	}

	.testimonial-benefits-content ul li::before{
		font-size: 14px;
	}

	.agency-benefits{
		padding: 40px 0 10px;
	}

	.agency-benefits::before{
		width: 210px;
		height: 220px;
	}
	
	.benefits-steps-item{
		padding: 20px;
	}

	.benefits-steps-item .icon-box,
	.benefits-steps-no{
		margin-bottom: 30px;
	}

	.benefits-steps-item .icon-box img{
		max-width: 50px;
	}

	.benefits-steps-content h3{
		font-size: 20px;
	}

	.our-blog{
		padding: 40px 0 10px;
	}

	.post-featured-image{
		margin-bottom: 20px;
	}

	.post-item-content{
		margin-bottom: 15px;
	}

	.post-item-content h3{
		font-size: 20px;
	}

	.footer-work-together{
		padding: 40px 0;
	}

	.footer-work-together::before{
		top: 0;
		width: 245px;
		height: 355px;
	}

	@keyframes circlmoveerotate{
		from{
			transform: translateY(0) rotate(0deg);
		  }
		to{
			transform: translateY(100px) rotate(360deg);
		}
	}

	.work-together-content{
		background-size: cover;
	}

	.footer-work-together h3{
		font-size: 20px;
		margin-bottom: 10px;
	}

	.footer-work-together h2{
		font-size: 120px;
	}

	.footer-work-together .work-together-btn a{
		width: 120px;
		height: 120px;
	}

	.footer-main{
		padding: 40px 0 0;
	}

	.footer-logo{
		margin-bottom: 40px;
	}

	.footer-contact-item{
		margin-bottom: 20px;
	}

	.footer-contact-content p{
		font-size: 20px;
	}

	.footer-newsletter-form h3,
	.footer-links h3{
		font-size: 20px;
		margin-bottom: 20px;
	}

	.footer-links ul li{
		margin-bottom: 10px;
	}

	.footer-newsletter-form{
		margin-top: 30px;
	}

	.footer-newsletter-form .form-group .form-control{
		width: 80%;
	}

	.footer-social-links{
		margin-top: 30px;
	}

	.footer-copyright{
		padding: 30px 0;
		margin-top: 40px;
	}

	.page-header{
		padding: 182px 0 80px;
	}

	.page-header-box h1{
		font-size: 70px;
	}

	.page-header-box ol li.breadcrumb-item{
		font-size: 16px;
	}

	.our-scrolling-ticker.subpages-scrolling-ticker .scrolling-content span{
		font-size: 20px;
	}

	.our-approach{
		padding: 40px 0 10px;
	}

	.mission-vision-item{
		padding: 30px;
	}

	.mission-vision-image{
		margin-bottom: 15px;
	}

	.mission-vision-item .mission-vision-content h3{
		font-size: 20px;
		margin-bottom: 15px;
	}

	.who-we-are{
		padding: 40px 0;
	}

	.who-we-are::before{
		top: -13%;
		width: 210px;
		height: 220px;
	}

	.who-we-are .who-we-are-content{
		padding-right: 0px;
		margin-bottom: 30px;
	}

	.experts-counters-list{
		gap: 40px;
	}

	.experts-counter-box{
		width: calc(50% - 20px);
		gap: 40px;
	}

	.experts-counter-box:nth-child(even){
		margin-top: 40px;
	}

	.experts-counter-content h2{
		font-size: 40px;
	}

	.executive-partners{
		padding: 40px 0;
	}

	.executive-partners-box{
		padding: 50px;
	}

	.executive-partners-box .section-title{
		margin-bottom: 30px;
	}

	.our-team{
		padding: 40px 0 10px;
	}

	.our-team::before{
		top: -5%;
		width: 250px;
        height: 280px;
	}

    .team-image figure, .team-image img{
        aspect-ratio: 1 / 1.3;
    }

    .team-content h3{
        font-size: 20px;
    }

	.our-faqs{
        padding: 40px 0;
    }

    .faq-images{
        max-width: 80%;
        margin: 0 auto 30px;
        padding: 100px 80px 0 0;
    }

    .faq-img-1 img{
        max-width: 285px;
    }

    .faq-img-2 img{
        aspect-ratio: 1 / 0.9;
    }

    .our-faq-section .accordion-header .accordion-button{
        font-size: 20px;
    }

	.page-services{
		padding: 80px 0 10px;
	}

	.page-service-single{
        padding: 80px 0 40px;
    }

	.page-service-single::before{
        width: 300px;
        height: 330px;
    }

	.page-service-single::after{
        width: 250px;
        height: 280px;
    }

    .service-single-content{
        margin: 0 0 40px 0;
    }

	.service-feature-image{
        margin-bottom: 30px;
    }

    .service-feature-image img{
        aspect-ratio: 1 / 0.59;
    }

    .service-entry{
        margin-bottom: 30px;
    }

    .service-entry h2{
        font-size: 40px;
    }

    .service-entry-list-image{
        margin: 30px 0;
		gap: 20px;
    }

	.service-entry-list{
		width: calc(55% - 10px);
	}

    .service-entry-list ul li{
        margin-bottom: 15px;
		padding-left: 25px;
    }

    .service-entry-list ul li::before{
        font-size: 16px;
    }

	.service-entry-image{
		width: calc(45% - 10px);
	}

    .process-step-content{
        padding: 30px;
    }

    .process-step-header{
        margin-bottom: 50px;
    }

    .process-step-header .icon-box img{
        max-width: 48px;
    }

    .process-step-no h3{
        font-size: 20px;
    }

    .process-step-body h3{
        font-size: 20px;
    }

	.process-step-image img{
		aspect-ratio: 1 / 0.8;
	}

	.service-sidebar{
		position: initial;
	}

    .service-catagery-list{
        margin-bottom: 40px;
    }

    .service-catagery-list h3{
        font-size: 20px;
        padding: 30px 30px 20px;
    }

    .service-catagery-list ul{
        padding: 20px 30px 30px;
    }

    .service-catagery-list ul li{
        padding-bottom: 20px;
        margin-bottom: 20px;
    }

    .sidebar-cta-box{
        padding: 30px;
    }

    .sidebar-cta-box .icon-box{
        margin-bottom: 30px;
    }

    .sidebar-cta-box .icon-box img{
        max-width: 50px;
    }

    .cta-contact-content h3{
        font-size: 20px;
        margin-bottom: 10px;
    }

	.page-blog{
		padding: 80px 0 40px;
	}
	
	.page-blog::before{
		width: 300px;
		height: 330px;
	}
	
	.post-item{
		height: calc(100% - 30px);
		margin-bottom: 30px;
	}

	.page-pagination{
		margin-top: 10px;
	}

	.page-single-post{
		padding: 80px 0 40px;
	}

	.post-image{
		margin-bottom: 20px;
	}

	.post-entry blockquote{
		background-position: 25px 25px;
        background-size: 50px;
        padding: 25px 25px 25px 90px;
        margin-bottom: 20px;
	}

	.post-entry blockquote p{
		font-size: 18px;
	}

	.post-entry h2{
		font-size: 40px;
	}

	.post-entry ul li{
		font-size: 16px;
	}

	.post-tags{
		margin-bottom: 20px;
	}

	.post-social-sharing ul{
		text-align: left;
	}

	.tag-links{
		font-size: 22px;
	}

	.post-tags .tag-links a{
		font-size: 16px;
		padding: 6px 15px;
	}

	.page-project{
		padding: 80px 0 10px;
	}

	.page-project::before{
        width: 300px;
        height: 330px;
    }

	.page-project-single{
        padding: 80px 0 40px;
    }

	.page-project-single::before{
		top: 20%;
        width: 210px;
        height: 220px;
    }

	.page-project-single::after{
        width: 250px;
        height: 280px;
    }

    .project-single-content{
        margin-right: 0;
        margin-bottom: 30px;
    }

    .project-single-image{
        margin-bottom: 30px;
    }

    .project-single-content img{
        aspect-ratio: 1 / 0.57;
    }

	.project-challenges,
	.project-info,
    .project-entry{
        margin-bottom: 30px;
    }

    .project-entry p{
        margin-bottom: 20px;
    }

    .project-entry h2{
        font-size: 40px;
        margin-bottom: 20px;
    }

    .project-entry ul li{
        margin-bottom: 15px;
		padding-left: 25px;
    }

	.project-entry ul li::before{
		font-size: 16px;
	}

    .project-solution-rating{
        margin-top: 0;
    }

    .project-counter-content h3{
        font-size: 34px;
    }

	.project-sidebar{
		position: initial;
	}

    .project-catagery-list{
        margin-bottom: 40px;
    }

    .category-item-list{
        padding: 30px 30px 20px;
    }

    .category-list-item h3{
        font-size: 20px;
        margin-bottom: 5px;
    }

    .category-social-link{
        padding: 30px;
    }

    .category-social-link .social-links{
        font-size: 20px;
    }

	.page-team{
		padding: 80px 0 10px;
	}

	.page-team::before{
        width: 245px;
        height: 355px;
    }

	.page-team-single{
        padding: 80px 0 40px;
    }

	.page-team-single::before{
		top: 40%;
		width: 250px;
        height: 280px;
	}

    .team-single-content{
        margin-right: 0;
    }

    .team-info-box{
        margin-bottom: 40px;
    }

    .team-info-header{
		align-items: center;
        margin-bottom: 30px;
    }

    .team-single-content h2{
        font-size: 40px;
    }

    .team-contact-box{
        padding-bottom: 20px;
        margin-bottom: 20px;
    }

    .team-contact-box .icon-box{
        width: 65px;
        height: 65px;
    }

    .team-contact-box .icon-box img{
        max-width: 30px;
    }

    .team-contact-content{
        width: calc(100% - 95px);
    }

    .team-contact-content h3{
        font-size: 20px;
        margin-bottom: 5px;
    }

    .team-personal-info{
        margin-bottom: 40px;
    }

    .team-career-list{
        margin-top: 20px;
    }

    .team-career-list ul{
        row-gap: 15px;
    }

	.team-single-sidebar{
		position: initial;
		margin-bottom: 30px;
	}

    .team-single-image{
        margin-bottom: 40px;
    }

    .team-single-image img{
        aspect-ratio: 1 / 0.8;
        object-position: top center;
    }

    .team-single-contact-form h3{
        font-size: 28px;
        padding: 30px;
    }

    .team-single-contact-form form{
        padding: 30px;
    }

	.page-pricing{
        padding: 80px 0 40px;
    }

    .pricing-box-list{
        gap: 30px;
    }

    .pricing-box{
        width: calc(50% - 15px);
        padding: 30px;
    }

    .pricing-box-content{
        gap: 15px;
        margin-bottom: 30px;
        padding-bottom: 30px;
    }

    .pricing-title{
        width: calc(52% - 7.5px);
    }

    .pricing-title h3{
        font-size: 26px;
        margin-bottom: 5px;
    }

    .pricing-price{
        width: calc(48% - 7.5px);
    }
    
    .pricing-price h2{
        font-size: 40px;
    }

    .pricing-price h2 sup{
        font-size: 20px;
    }

    .pricing-price h2 sub{
        font-size: 14px;
    }

    .pricing-list ul{
        gap: 15px;
    }

    .pricing-list ul li{
        width: 100%;
    }

    .pricing-list{
        margin-bottom: 30px;
    }

	.page-testimonial{
        padding: 80px 0 40px;
    }

    .testimonial-box-list{
        gap: 40px;
    }

    .testimonial-box-item{
        padding: 40px 30px;
        gap: 30px;
    }

    .client-author-image{
        width: calc(32% - 15px);
    }

    .client-testimonial-content{
        width: calc(68% - 15px);
    }

    .client-testimonial-rating{
        margin-bottom: 10px;
    }

	.client-testimonial-info p{
		font-size: 18px;
	}

    .client-author-title h3{
        font-size: 20px;
    }

	.page-gallery{
		padding: 80px 0 10px;
	}

	.page-video-gallery{
		padding: 80px 0 10px;
	}

	.page-faqs{
        padding: 80px 0 40px;
	}

	.page-faqs-catagery{
		margin-right: 0px;
	}

    .page-faq-accordion{
        margin-bottom: 40px;
    }

    .faq-sidebar{
        position: initial;
		margin-bottom: 30px;
    }

    .faq-catagery-list{
        margin-bottom: 40px;
    }

    .faq-catagery-list ul{
        padding: 30px;
    }

    .faq-catagery-list ul li{
        padding-bottom: 20px;
        margin-bottom: 20px;
    }

	.page-contact-us{
        padding: 80px 0 40px;
    }

    .contact-information{
        margin-bottom: 30px;
    }

    .contact-info-box{
        gap: 20px;
    }

    .contact-info-box .info-box-1, 
    .contact-info-box .info-box-2{
        padding: 20px 30px;
    }

    .contact-info-item .icon-box{
        margin-bottom: 20px;
    }

    .contact-info-item .icon-box img{
        max-width: 40px;
    }

    .contact-info-box .info-box-2 .contact-item-content{
        width: calc(100% - 70px);
    }

    .contact-item-content h3{
        font-size: 20px;
    }

    .contact-us-form{
        margin: 0;
        padding: 30px;
    }

	.google-map{
		padding: 40px 0;
	}

    .google-map-iframe,
	.google-map-iframe iframe{
        height: 450px;
    }

	.error-page{
		padding: 80px 0 40px;
	}
	
	.error-page-image{
		margin-bottom: 20px;
	}

	.error-page-image img{
		max-width: 80%;
	}

	.error-page-content-heading{
		margin-bottom: 15px;
	}

	.error-page-content-body p{
		margin-bottom: 20px;
	}
}

@media only screen and (max-width: 767px){

	body{
		background-size: 200% auto;
	}

	.section-title h1{
        font-size: 34px;
    }

	.section-title h2{
        font-size: 30px;
    }

	.hero-content{
		text-align: center;
	}

	.hero-content .section-title{
        margin-bottom: 30px;
    }

	.hero-content-body{
		display: block;
	}

	.hero-content-video{
		width: 100%;
		justify-content: center;
		margin-bottom: 30px;
	}

	.hero-content-video .video-play-button a{
		height: 80px;
		width: 80px;
	}

	.hero-content-video .video-play-button a i{
		font-size: 25px;
	}

	.learn-more-circle img{
		max-width: 80px;
	}

	.hero-video-content{
        width: 100%;
    }

	.scrolling-ticker-box{
		--gap: 20px;
	}

	.scrolling-content span{
        font-size: 28px;
    }

	.scrolling-content span img{
		max-width: 20px;
		margin-right: 20px;
	}

	.about-agency::before{
        width: 250px;
        height: 280px;
    }

	.about-agency-list{
        padding-left: 20px;
    }

	.agency-item-content h3:after{
        left: -30px;
    }

	.agency-item-content h3{
        font-size: 18px;
    }

	.service-item{
        padding: 20px;
    }

	.service-item-body h3{
        font-size: 18px;
    }

	.service-footer p{
		font-size: 12px;
	}

	.digital-success-box{
        gap: 30px;
        padding: 40px 30px;
    }

	.digital-success-box::before{
		left: 10px;
		top: 10px;
		border-radius: 20px;
		width: calc(100% - 20px);
		height: calc(100% - 10px);
	}

	.success-counter-box{
		gap: 20px;
	}

	.success-counter-item{
		width: 100%;
	}

	.success-counter-item h2{
        font-size: 30px;
    }

	.success-list-item{
		background-size: 18px auto;
		margin-bottom: 30px;
	}

	.success-list-item p span{
        font-size: 18px;
    }

	.why-choose-us::before{
        top: 30%;
        width: 200px;
        height: 230px;
    }

	.why-choose-item h3{
        font-size: 18px;
    }

	.agency-social-item{
        margin: 15px 0 30px 15px;
    }

	.agency-social-item .icon-box{
        top: -15px;
        left: -15px;
    }

	.agency-social-item .icon-box a{
        width: 50px;
        height: 50px;
    }

	.agency-social-item .icon-box i{
        font-size: 20px;
    }

	.agency-social-content h3{
        font-size: 18px;
    }

	.how-it-work::before{
        width: 180px;
        height: 220px;
    }

	.work-process-header{
        margin-bottom: 20px;
    }

	.work-process-title h3{
        font-size: 18px;
    }

	.work-process-content{
        margin-bottom: 30px;
    }

	.work-process-body{
        padding-top: 20px;
    }

	.work-process-no h3{
        font-size: 18px;
    }

	.work-process-no h2{
        font-size: 28px;
    }

	.our-features::before{
		right: -70px;
		top: 13%;
		width: 150px;
		height: 135px;
	}

	.digital-features-item.features-item-2,
	.digital-features-item.features-item-1{
		width: 100%;
	}

	.digital-features-item.features-item-1 .digital-features-image img{
        aspect-ratio: 1 / 0.72;
    }

	.digital-features-item{
        padding: 20px;
    }

	.digital-features-image{
        margin-bottom: 20px;
    }

	.digital-features-content h3{
		font-size: 18px;
	}

	.agency-supports-header{
		display: block;
	}

	.agency-supports-content,
	.agency-free-consultation{
        width: 100%;
    }

	.agency-supports-content h3{
        font-size: 18px;
    }

	.agency-free-consultation{
		text-align: left;
		margin-top: 20px;
	}

	.agency-supports-slider::before{
		background: linear-gradient(280deg, #030709 0%, rgba(3, 7, 9, 0) 60.97%);
		width: 150px;
	}

	.agency-supports-slider::after{
		background: linear-gradient(90deg, #030709 0%, rgba(3, 7, 9, 0) 60.97%);
		width: 150px;
	}

	.project-btn a{
        width: 60px;
        height: 60px;
    }

	.project-btn a img{
        max-width: 26px;
    }

	.project-content h3{
        font-size: 18px;
    }

	.testimonial-review-box{
        padding: 20px;
    }

	.testimonial-review-header h2{
        font-size: 40px;
    }

	.testimonial-review-content h3{
        font-size: 18px;
    }

	.testimonial-company-logo,
	.testimonial-rating{
		margin-bottom: 15px;
	}

	.testimonial-content p{
        font-size: 18px;
    }

	.author-content h3{
        font-size: 18px;
    }

	.testimonial-btn{
		position: relative;
		justify-content: center;
		margin-top: 30px;
	}

	.testimonial-slider .testimonial-button-next,
	.testimonial-slider .testimonial-button-prev{
        width: 45px;
        height: 45px;
    }

	.testimonial-benefits-box{
		gap: 30px;
		padding: 30px 20px;
	}

	.testimonial-benefits-item{
        width: 100%;
    }

	.testimonial-benefits-item::before{
		top: auto;
		bottom: -15px;
		right: 0;
		border-right: none;
		border-bottom: 1px solid var(--divider-color);
		width: 100%;
		height: 1px;
		z-index: 1;
	}

	.testimonial-benefits-item:last-child:before{
		display: none;
	}

	.testimonial-benefits-content h3{
        font-size: 18px;
		margin-bottom: 10px;
    }

	.benefits-steps-item .icon-box,
	.benefits-steps-no{
        margin-bottom: 20px;
    }

	.benefits-steps-no h3{
		font-size: 18px;
	}

	.benefits-steps-content h3{
        font-size: 18px;
    }

	.post-item-content h3{
		font-size: 18px;
	}

	.footer-work-together::before{
		left: -90px;
        width: 180px;
        height: 190px;
    }

	.footer-work-together h3{
        font-size: 18px;
    }

	.footer-work-together h2{
        font-size: 55px;
    }

	.work-together-btn{
		top: 56%;
	}

	.footer-work-together .work-together-btn a{
        width: 85px;
        height: 85px;
    }

	.footer-work-together .work-together-btn a span{
		font-size: 12px;
	}

	.about-footer{
		margin-bottom: 30px;
	}

	.footer-contact-content p{
        font-size: 18px;
    }

	.footer-logo{
        margin-bottom: 30px;
    }

	.footer-newsletter-form h3,
	.footer-links h3{
        font-size: 18px;
        margin-bottom: 15px;
    }

	.footer-newsletter-form .form-group .form-control{
        width: 57%;
    }

	.footer-copyright{
        padding: 20px 0;
        margin-top: 30px;
    }

	.page-header-box h1{
		font-size: 34px;
	}

	.our-scrolling-ticker.subpages-scrolling-ticker .scrolling-content span{
		font-size: 18px;
	}

	.our-scrolling-ticker.subpages-scrolling-ticker .scrolling-content span img{
		max-width: 16px;
	}

	.mission-vision-item{
        padding: 20px;
    }

	.mission-vision-item .mission-vision-content h3{
		font-size: 18px;
	}

	.experts-rating-video-image{
		width: 100%;
		margin-bottom: 20px;
	}

	.who-we-are-client{
		width: 100%;
		padding-left: 0px;
	}

	.experts-counter-box{
        width: 100%;
        gap: 30px;
    }

	.experts-counter-box:nth-child(even){
        margin-top: 0px;
    }

	.experts-counter-item{
		padding: 20px;
	}

	.experts-counter-box.expert-box-2 .experts-counter-item:nth-child(odd){
		background: url(../images/who-we-are-counter-bg.png);
	}

	.experts-counter-box.expert-box-2 .experts-counter-item:nth-child(even){
		background: transparent;
	}

	.experts-counter-content h2{
        font-size: 30px;
    }

	.executive-partners-box{
        padding: 30px;
    }

	.our-partners-list{
		gap: 20px;
	}

	.our-partners-list .company-logo{
		width: calc(50% - 10px);
		padding: 15px;
	}

	.faq-images{
        max-width: 100%;
        padding: 80px 50px 0 0;
    }

    .faq-img-1 img{
        max-width: 185px;
    }

    .faq-cta-box a{
        font-size: 14px;
        padding: 8px 15px;
    }

    .our-faq-section .accordion-item{
        margin-bottom: 20px;
    }

    .our-faq-section .accordion-header .accordion-button{
        font-size: 18px;
        padding: 12px 40px 12px 12px;
    }

    .our-faq-section .accordion-item .accordion-button::after,
    .our-faq-section .accordion-item .accordion-button.collapsed::after{
        right: 12px;
        font-size: 12px;
        width: 20px;
        height: 20px;
    }

    .our-faq-section .accordion-item .accordion-body{
        padding: 0 12px 12px 12px;
    }

	.page-service-single::before{
        width: 250px;
        height: 280px;
    }

	.page-service-single::after{
        width: 250px;
        height: 280px;
    }

	.service-feature-image img{
        aspect-ratio: 1 / 0.78;
    }

    .service-entry h2{
        font-size: 30px;
    }

    .service-entry-list-image{
        gap: 30px;
    }

    .service-entry-list,
    .service-entry-image{
        width: 100%;
    }

    .service-entry-list ul li{
        font-size: 14px;
		padding-left: 20px;
    }

	.service-entry-list ul li::before{
        font-size: 14px;
    }

    .service-entry-image img{
        aspect-ratio: 1 / 0.68;
        object-position: top center;
    }

    .service-process-steps{
        gap: 20px;
        margin: 0;
    }

    .process-step-item{
        gap: 20px;
    }

    .process-step-content,
    .process-step-image{
        width: 100%;
    }

    .process-step-content{
        padding: 20px;
    }

    .process-step-header{
        margin-bottom: 50px;
    }

    .process-step-header .icon-box img{
        max-width: 36px;
    }

    .process-step-no h3{
        font-size: 18px;
    }

    .process-step-body h3{
        font-size: 18px;
        margin-bottom: 15px;
    }

    .process-step-image img{
        aspect-ratio: 1 / 0.72;
    }

    .service-catagery-list{
        margin-bottom: 30px;
    }

    .service-catagery-list h3{
        font-size: 18px;
        padding: 20px;
    }

    .service-catagery-list ul{
        padding: 20px;
    }

	.service-catagery-list ul li{
		padding-bottom: 15px;
		margin-bottom: 15px;
	}

    .service-catagery-list ul li a::before{
        width: 22px;
        height: 22px;
    }
    
    .sidebar-cta-box{
        padding: 20px;
    }

    .sidebar-cta-box .icon-box{
        margin-bottom: 25px;
    }

    .sidebar-cta-box .icon-box img{
        max-width: 40px;
    }

    .cta-contact-content h3{
        font-size: 18px;
    }

    .cta-contact-btn a{
        padding: 10px 25px;
    }

    .cta-contact-btn a img{
        max-width: 22px;
        margin-right: 10px;
    }

	.page-blog::before{
        width: 250px;
        height: 280px;
    }

	.post-image figure,
	.post-image img{
		aspect-ratio: 1 / 0.70;
	}

	.post-entry blockquote{
		background-position: 20px 20px;
        padding: 70px 20px 20px 20px;
	}
	
	.post-entry h2{
		font-size: 28px;
	}

	.tag-links{
		font-size: 20px;
	}

	.page-project::before{
        width: 250px;
        height: 280px;
    }

	.page-project-single::before{
		width: 210px;
        height: 220px;
    }

	.page-project-single::after{
		top: 53%;
        width: 200px;
        height: 230px;
    }

	.project-single-content{
        margin-bottom: 20px;
    }

    .project-single-content img{
        aspect-ratio: 1 / 0.67;
    }

    .project-entry h2{
        font-size: 30px;
    }

    .project-entry ul li{
        font-size: 14px;
        margin-bottom: 10px;
    }

    .project-solution-rating{
        gap: 15px;
    }

    .project-rating-counter .icon-box i{
        font-size: 36px;
    }

    .project-counter-content{
        width: calc(100% - 56px);
    }

    .project-counter-content h3{
        font-size: 28px;
    }

    .project-rating-content,
    .project-rating-counter{
        width: 100%;
    }

    .category-item-list{
        padding: 20px;
    }

    .category-list-item{
        padding-bottom: 15px;
        margin-bottom: 15px;
    }

    .category-list-item h3{
        font-size: 18px;
    }

    .category-social-link{
        padding: 20px;
    }

	.category-social-link span{
		font-size: 20px;
	}

	.page-team::before{
        width: 180px;
        height: 190px;
    }

	.page-team-single::before{
		width: 200px;
        height: 230px;
    }

	.team-info-box{
        margin-bottom: 30px;
    }

    .team-info-header{
        margin-bottom: 30px;
        gap: 10px;
    }

    .team-info-title,
    .team-info-social-list{
        width: 100%;
    }

	.team-info-social-list ul{
		text-align: left;
	}

    .team-single-content p{
        margin-bottom: 15px;
    }

    .team-single-content h2{
        font-size: 30px;
    }

    .team-contact-box{
        padding-bottom: 15px;
        margin-bottom: 15px;
    }

	.team-contact-box .icon-box{
		margin-right: 20px;
	}

    .team-contact-content{
        width: calc(100% - 85px);
    }

    .team-contact-content h3{
        font-size: 18px;
    }

    .team-personal-info{
        margin-bottom: 30px;
    }

    .team-career-list ul{
        gap: 10px;
    }

    .team-career-list ul li{
        width: 100%;
		padding-left: 25px;
    }

    .team-career-list ul li::before{
        font-size: 16px;
    }

    .team-single-image{
        margin-bottom: 25px;
    }

    .team-single-image img{
        aspect-ratio: 1 / 1.08;
    }

    .team-single-contact-form h3{
        font-size: 30px;
        padding: 20px;
    }

    .team-single-contact-form form{
        padding: 20px;
    }

	.pricing-box{
        width: 100%;
        padding: 20px;
    }

    .pricing-box-content{
        margin-bottom: 20px;
        padding-bottom: 20px;
    }

    .pricing-title h3{
        font-size: 22px;
    }

    .pricing-price h2{
        font-size: 30px;
    }

    .pricing-price h2 sup{
        font-size: 16px;
	}

	.pricing-list ul li{
		padding-left: 25px;
	}

    .pricing-list ul li::before{
        font-size: 16px;
    }

	.testimonial-box-item{
        padding: 20px 15px;
        gap: 20px;
        text-align: center;
    }

    .client-author-image{
        width: 100%;
        max-width: 120px;
        margin: 0 auto;
    }

    .client-testimonial-content{
        width: 100%;
    }

    .client-testimonial-info p{
        font-size: 16px;
    }

    .client-author-content{
        gap: 15px;
    }

    .client-author-title,
    .client-author-logo{
        width: 100%;
        text-align: center;
    }

    .client-author-title h3{
        font-size: 18px;
    }

	.page-faq-accordion{
        margin-bottom: 30px;
    }

    .faq-catagery-list{
        margin-bottom: 30px;
    }

	.faq-catagery-list ul{
        padding: 20px;
    }

	.contact-info-box .info-box-1,
    .contact-info-box .info-box-2{
        padding: 20px;
        gap: 40px;
    }

    .contact-info-box .info-box-1 .contact-info-item::before{
		top: auto;
		bottom: 0;
		left: 50%;
		transform: translate(-50%, 20px);
		height: 1px;
		width: 100%;
    }

	.contact-info-box .info-box-1 .contact-info-item:nth-child(2n + 2):before{
		display: block;
	}

	.contact-info-box .info-box-1 .contact-info-item:last-child:before{
		display: none;
	}

    .contact-info-box .info-box-1 .contact-info-item{
        display: flex;
        width: 100%;
    }

    .contact-info-item .icon-box img{
        max-width: 32px;
    }

    .contact-info-box .info-box-1 .icon-box,
    .contact-info-box .info-box-2 .icon-box{
        margin: 0 25px 0 0;
    }

    .contact-info-box .info-box-1 .contact-item-content,
    .contact-info-box .info-box-2 .contact-item-content{
        width: calc(100% - 57px);
        text-align: left;
    }

    .contact-item-content h3{
        font-size: 18px;
    }

    .contact-us-form{
        padding: 20px;
    }

    .google-map-iframe,
    .google-map-iframe iframe{
        height: 350px;
    }
}