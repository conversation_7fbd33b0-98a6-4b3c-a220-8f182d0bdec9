<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out" 
         :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }">
      
      <!-- Logo -->
      <div class="flex items-center justify-center h-16 px-4 bg-blue-600">
        <h1 class="text-xl font-bold text-white">Admin Panel</h1>
      </div>

      <!-- Navigation -->
      <nav class="mt-8">
        <div class="px-4 space-y-2">
          <router-link
            v-for="item in navigation"
            :key="item.name"
            :to="item.to"
            class="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200"
            :class="[
              $route.name === item.name
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            ]"
            v-if="!item.requiresRole || authStore.hasPermission(item.requiresRole)"
          >
            <component :is="item.icon" class="w-5 h-5 mr-3" />
            {{ item.label }}
          </router-link>
        </div>
      </nav>

      <!-- User info -->
      <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <span class="text-sm font-medium text-white">
                {{ authStore.userDisplayName.charAt(0).toUpperCase() }}
              </span>
            </div>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-700">{{ authStore.userDisplayName }}</p>
            <p class="text-xs text-gray-500">{{ authStore.user?.role }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <div class="lg:pl-64">
      <!-- Top bar -->
      <div class="sticky top-0 z-40 flex h-16 bg-white shadow">
        <button
          @click="sidebarOpen = !sidebarOpen"
          class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 lg:hidden"
        >
          <Bars3Icon class="w-6 h-6" />
        </button>

        <div class="flex flex-1 justify-between px-4">
          <div class="flex flex-1">
            <!-- Search can be added here -->
          </div>
          <div class="ml-4 flex items-center md:ml-6">
            <!-- Language selector -->
            <select
              v-model="currentLanguage"
              @change="changeLanguage"
              class="mr-4 px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="en">English</option>
              <option value="tr">Türkçe</option>
              <option value="de">Deutsch</option>
              <option value="fr">Français</option>
              <option value="es">Español</option>
            </select>

            <!-- User menu -->
            <div class="relative">
              <button
                @click="userMenuOpen = !userMenuOpen"
                class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span class="text-sm font-medium text-white">
                    {{ authStore.userDisplayName.charAt(0).toUpperCase() }}
                  </span>
                </div>
              </button>

              <!-- User dropdown -->
              <div
                v-if="userMenuOpen"
                class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5"
                @click.away="userMenuOpen = false"
              >
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                <button
                  @click="logout"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Sign out
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Page content -->
      <main class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <router-view />
        </div>
      </main>
    </div>

    <!-- Mobile sidebar overlay -->
    <div
      v-if="sidebarOpen"
      class="fixed inset-0 z-40 lg:hidden"
      @click="sidebarOpen = false"
    >
      <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import {
  Bars3Icon,
  HomeIcon,
  DocumentTextIcon,
  PhotoIcon,
  CogIcon,
  LanguageIcon,
  UsersIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const { locale } = useI18n()
const authStore = useAuthStore()

const sidebarOpen = ref(false)
const userMenuOpen = ref(false)
const currentLanguage = ref(locale.value)

const navigation = [
  {
    name: 'admin-dashboard',
    label: 'Dashboard',
    to: '/admin/dashboard',
    icon: HomeIcon
  },
  {
    name: 'admin-content',
    label: 'Content',
    to: '/admin/content',
    icon: DocumentTextIcon,
    requiresRole: 'editor'
  },
  {
    name: 'admin-media',
    label: 'Media',
    to: '/admin/media',
    icon: PhotoIcon,
    requiresRole: 'editor'
  },
  {
    name: 'admin-translations',
    label: 'Translations',
    to: '/admin/translations',
    icon: LanguageIcon,
    requiresRole: 'editor'
  },
  {
    name: 'admin-users',
    label: 'Users',
    to: '/admin/users',
    icon: UsersIcon,
    requiresRole: 'admin'
  },
  {
    name: 'admin-settings',
    label: 'Settings',
    to: '/admin/settings',
    icon: CogIcon,
    requiresRole: 'admin'
  }
]

const changeLanguage = () => {
  locale.value = currentLanguage.value
  localStorage.setItem('language', currentLanguage.value)
}

const logout = async () => {
  try {
    await authStore.logout()
    router.push('/admin/login')
  } catch (error) {
    console.error('Logout error:', error)
  }
}

onMounted(() => {
  // Set language from localStorage
  const savedLanguage = localStorage.getItem('language')
  if (savedLanguage) {
    currentLanguage.value = savedLanguage
    locale.value = savedLanguage
  }
})
</script>

<style scoped>
/* Additional styles if needed */
</style>
