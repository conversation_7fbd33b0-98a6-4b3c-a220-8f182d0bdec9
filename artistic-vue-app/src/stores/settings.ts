import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface ContactInfo {
  phone: string
  email: string
  address: string
}

export interface CompanyInfo {
  name: string
  description: string
  logo: string
  footerLogo: string
}

export interface SocialLinks {
  twitter: string
  facebook: string
  instagram: string
  linkedin: string
  pinterest: string
}

export interface SiteSettings {
  title: string
  description: string
  keywords: string
  favicon: string
  language: string
  theme: 'light' | 'dark'
  colors: {
    primary: string
    secondary: string
    accent: string
  }
}

export const useSettingsStore = defineStore('settings', () => {
  // Contact Information
  const contactInfo = ref<ContactInfo>({
    phone: '(309) 8855-314',
    email: '<EMAIL>',
    address: '123 Creative Lane London, SW1A 1AA United Kingdom'
  })

  // Company Information
  const companyInfo = ref<CompanyInfo>({
    name: 'Artistic',
    description: 'Creative Digital Agency',
    logo: '/images/logo.svg',
    footerLogo: '/images/footer-logo.svg'
  })

  // Social Media Links
  const socialLinks = ref<SocialLinks>({
    twitter: '#',
    facebook: '#',
    instagram: '#',
    linkedin: '#',
    pinterest: '#'
  })

  // Site Settings
  const siteSettings = ref<SiteSettings>({
    title: 'Artistic - Creative Digital Agency',
    description: 'Innovative solutions for digital world',
    keywords: 'digital agency, web design, marketing, creative',
    favicon: '/images/favicon.png',
    language: 'en',
    theme: 'light',
    colors: {
      primary: '#007bff',
      secondary: '#6c757d',
      accent: '#28a745'
    }
  })

  // Computed properties
  const fullCompanyName = computed(() => 
    `${companyInfo.value.name} - ${companyInfo.value.description}`
  )

  // Actions
  const updateContactInfo = (newContactInfo: Partial<ContactInfo>) => {
    contactInfo.value = { ...contactInfo.value, ...newContactInfo }
  }

  const updateCompanyInfo = (newCompanyInfo: Partial<CompanyInfo>) => {
    companyInfo.value = { ...companyInfo.value, ...newCompanyInfo }
  }

  const updateSocialLinks = (newSocialLinks: Partial<SocialLinks>) => {
    socialLinks.value = { ...socialLinks.value, ...newSocialLinks }
  }

  const updateSiteSettings = (newSiteSettings: Partial<SiteSettings>) => {
    siteSettings.value = { ...siteSettings.value, ...newSiteSettings }
  }

  const updateThemeColors = (colors: Partial<SiteSettings['colors']>) => {
    siteSettings.value.colors = { ...siteSettings.value.colors, ...colors }
  }

  // Load settings from API
  const loadSettings = async () => {
    try {
      // This would be an API call in a real application
      // const response = await api.get('/settings')
      // updateContactInfo(response.data.contact)
      // updateCompanyInfo(response.data.company)
      // updateSocialLinks(response.data.social)
      // updateSiteSettings(response.data.site)
      console.log('Settings loaded')
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }

  // Save settings to API
  const saveSettings = async () => {
    try {
      // This would be an API call in a real application
      // await api.post('/settings', {
      //   contact: contactInfo.value,
      //   company: companyInfo.value,
      //   social: socialLinks.value,
      //   site: siteSettings.value
      // })
      console.log('Settings saved')
    } catch (error) {
      console.error('Failed to save settings:', error)
    }
  }

  return {
    // State
    contactInfo,
    companyInfo,
    socialLinks,
    siteSettings,
    
    // Computed
    fullCompanyName,
    
    // Actions
    updateContactInfo,
    updateCompanyInfo,
    updateSocialLinks,
    updateSiteSettings,
    updateThemeColors,
    loadSettings,
    saveSettings
  }
})
