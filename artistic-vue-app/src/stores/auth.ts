import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { db, type User } from '@/services/database'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || 'viewer')
  const userDisplayName = computed(() => user.value?.name || user.value?.email || 'User')

  // Helper function to generate session token
  const generateSessionToken = () => {
    return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now()
  }

  // Actions
  const login = async (email: string, password: string) => {
    isLoading.value = true
    error.value = null

    try {
      // Use LokalJS database instead of API
      const foundUser = await db.findUserByEmail(email)
      
      if (foundUser && foundUser.password === password) {
        // In production, use proper password hashing
        const sessionToken = generateSessionToken()
        token.value = sessionToken
        user.value = foundUser
        
        // Store token in localStorage
        localStorage.setItem('auth_token', sessionToken)
        localStorage.setItem('user_data', JSON.stringify(foundUser))
        
        return { user: foundUser, token: sessionToken }
      } else {
        throw new Error('Invalid email or password')
      }
    } catch (err: any) {
      error.value = err.message || 'Login failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: {
    name: string
    email: string
    password: string
    role?: 'admin' | 'editor' | 'viewer'
  }) => {
    try {
      isLoading.value = true
      error.value = null

      // Check if user already exists
      const existingUser = await db.findUserByEmail(userData.email)
      if (existingUser) {
        throw new Error('User with this email already exists')
      }

      // Create new user
      const newUser = await db.createUser({
        name: userData.name,
        email: userData.email,
        password: userData.password, // In production, hash this
        role: userData.role || 'viewer'
      })

      return newUser
    } catch (err: any) {
      error.value = err.message || 'Registration failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      // Clear local state
      user.value = null
      token.value = null
      error.value = null
      
      // Clear localStorage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
      
    } catch (err: any) {
      error.value = err.message || 'Logout failed'
      throw err
    }
  }

  const checkAuth = async () => {
    const storedToken = localStorage.getItem('auth_token')
    const storedUser = localStorage.getItem('user_data')
    
    if (storedToken && storedUser) {
      try {
        token.value = storedToken
        user.value = JSON.parse(storedUser)
        return true
      } catch (err) {
        // Invalid stored data, clear it
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_data')
        return false
      }
    }
    return false
  }

  const updateProfile = async (updates: Partial<User>) => {
    if (!user.value) throw new Error('No user logged in')
    
    try {
      isLoading.value = true
      error.value = null

      const updatedUser = await db.updateUser(user.value.$loki?.toString() || '', updates)
      if (updatedUser) {
        user.value = updatedUser
        localStorage.setItem('user_data', JSON.stringify(updatedUser))
        return updatedUser
      }
      throw new Error('Failed to update profile')
    } catch (err: any) {
      error.value = err.message || 'Profile update failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string) => {
    if (!user.value) throw new Error('No user logged in')
    
    try {
      isLoading.value = true
      error.value = null

      // Verify current password
      if (user.value.password !== currentPassword) {
        throw new Error('Current password is incorrect')
      }

      // Update password
      const updatedUser = await db.updateUser(user.value.$loki?.toString() || '', {
        password: newPassword // In production, hash this
      })

      if (updatedUser) {
        user.value = updatedUser
        localStorage.setItem('user_data', JSON.stringify(updatedUser))
        return true
      }
      throw new Error('Failed to change password')
    } catch (err: any) {
      error.value = err.message || 'Password change failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const hasPermission = (requiredRole: string) => {
    if (!user.value) return false
    
    const roleHierarchy = {
      'admin': 3,
      'editor': 2,
      'viewer': 1
    }
    
    const userRoleLevel = roleHierarchy[user.value.role as keyof typeof roleHierarchy] || 0
    const requiredRoleLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0
    
    return userRoleLevel >= requiredRoleLevel
  }

  const clearError = () => {
    error.value = null
  }

  // Initialize auth state on store creation
  checkAuth()

  return {
    // State
    user,
    token,
    isLoading,
    error,
    
    // Computed
    isAuthenticated,
    userRole,
    userDisplayName,
    
    // Actions
    login,
    register,
    logout,
    checkAuth,
    updateProfile,
    changePassword,
    hasPermission,
    clearError
  }
})
