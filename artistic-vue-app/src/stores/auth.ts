import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '@/services/api'

export interface User {
  _id: string
  username: string
  email: string
  role: 'admin' | 'editor' | 'viewer'
  isActive: boolean
  lastLogin?: Date
  profile?: {
    firstName?: string
    lastName?: string
    avatar?: string
    phone?: string
  }
  createdAt: Date
  updatedAt: Date
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isEditor = computed(() => user.value?.role === 'editor' || user.value?.role === 'admin')
  const userDisplayName = computed(() => {
    if (!user.value) return ''
    const { profile, username } = user.value
    if (profile?.firstName && profile?.lastName) {
      return `${profile.firstName} ${profile.lastName}`
    }
    return username
  })

  // Actions
  const login = async (email: string, password: string) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await apiService.login(email, password)
      
      token.value = response.token
      user.value = response.user
      
      // Store in localStorage
      localStorage.setItem('auth_token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))

      return response
    } catch (err: any) {
      error.value = err.response?.data?.error || 'Login failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: {
    username: string
    email: string
    password: string
    role?: string
  }) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await apiService.register(userData)
      return response
    } catch (err: any) {
      error.value = err.response?.data?.error || 'Registration failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      if (token.value) {
        await apiService.logout()
      }
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      // Clear state regardless of API call success
      user.value = null
      token.value = null
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user')
    }
  }

  const getCurrentUser = async () => {
    try {
      if (!token.value) return null

      isLoading.value = true
      const response = await apiService.getCurrentUser()
      user.value = response.user
      
      // Update localStorage
      localStorage.setItem('user', JSON.stringify(response.user))
      
      return response.user
    } catch (err: any) {
      console.error('Get current user error:', err)
      // If token is invalid, clear auth state
      if (err.response?.status === 401) {
        await logout()
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const initializeAuth = async () => {
    // Check if user data exists in localStorage
    const storedUser = localStorage.getItem('user')
    const storedToken = localStorage.getItem('auth_token')

    if (storedToken && storedUser) {
      try {
        token.value = storedToken
        user.value = JSON.parse(storedUser)
        
        // Verify token is still valid by fetching current user
        await getCurrentUser()
      } catch (err) {
        // Token is invalid, clear auth state
        await logout()
      }
    }
  }

  const updateProfile = async (profileData: Partial<User['profile']>) => {
    try {
      isLoading.value = true
      error.value = null

      // This would be an API call to update profile
      // For now, just update local state
      if (user.value) {
        user.value.profile = { ...user.value.profile, ...profileData }
        localStorage.setItem('user', JSON.stringify(user.value))
      }
    } catch (err: any) {
      error.value = err.response?.data?.error || 'Profile update failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const hasPermission = (requiredRole: 'admin' | 'editor' | 'viewer') => {
    if (!user.value) return false
    
    const roleHierarchy = {
      admin: 3,
      editor: 2,
      viewer: 1
    }
    
    return roleHierarchy[user.value.role] >= roleHierarchy[requiredRole]
  }

  return {
    // State
    user,
    token,
    isLoading,
    error,
    
    // Computed
    isAuthenticated,
    isAdmin,
    isEditor,
    userDisplayName,
    
    // Actions
    login,
    register,
    logout,
    getCurrentUser,
    initializeAuth,
    updateProfile,
    clearError,
    hasPermission
  }
})
