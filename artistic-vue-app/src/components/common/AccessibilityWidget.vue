<template>
  <div class="accessibility-widget">
    <!-- Accessibility Toggle Button -->
    <button
      @click="toggleWidget"
      class="accessibility-toggle"
      :aria-label="$t('accessibility.toggleWidget')"
      :aria-expanded="isOpen"
    >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 5.5V6.5L16.5 7V8.5C16.5 8.5 15.5 8.5 15.5 9.5S16.5 10.5 16.5 10.5V12L15 13L13.5 12V9.5C13.5 8.5 12.5 8.5 12.5 8.5V7L14 6.5V5.5L8 7V9L10.5 8.5V12L9 13L10.5 14V16.5C10.5 17.5 11.5 17.5 11.5 17.5C11.5 17.5 12.5 17.5 12.5 16.5V14L14 13L15.5 14V16.5C15.5 17.5 16.5 17.5 16.5 17.5C16.5 17.5 17.5 17.5 17.5 16.5V14L19 13L17.5 12V10.5C17.5 10.5 18.5 10.5 18.5 9.5S17.5 8.5 17.5 8.5V7L19 6.5V5.5L21 9Z"/>
      </svg>
    </button>

    <!-- Accessibility Panel -->
    <div
      v-if="isOpen"
      class="accessibility-panel"
      role="dialog"
      :aria-label="$t('accessibility.panelTitle')"
    >
      <div class="panel-header">
        <h2>{{ $t('accessibility.panelTitle') }}</h2>
        <button
          @click="closeWidget"
          class="close-btn"
          :aria-label="$t('accessibility.close')"
        >
          ×
        </button>
      </div>

      <div class="panel-content">
        <!-- Font Size Controls -->
        <div class="control-group">
          <h3>{{ $t('accessibility.fontSize') }}</h3>
          <div class="button-group">
            <button @click="decreaseFontSize" :aria-label="$t('accessibility.decreaseFont')">A-</button>
            <button @click="resetFontSize" :aria-label="$t('accessibility.resetFont')">A</button>
            <button @click="increaseFontSize" :aria-label="$t('accessibility.increaseFont')">A+</button>
          </div>
        </div>

        <!-- Contrast Controls -->
        <div class="control-group">
          <h3>{{ $t('accessibility.contrast') }}</h3>
          <div class="button-group">
            <button
              @click="toggleHighContrast"
              :class="{ active: settings.highContrast }"
              :aria-pressed="settings.highContrast"
            >
              {{ $t('accessibility.highContrast') }}
            </button>
            <button
              @click="toggleDarkMode"
              :class="{ active: settings.darkMode }"
              :aria-pressed="settings.darkMode"
            >
              {{ $t('accessibility.darkMode') }}
            </button>
          </div>
        </div>

        <!-- Reading Controls -->
        <div class="control-group">
          <h3>{{ $t('accessibility.reading') }}</h3>
          <div class="button-group">
            <button
              @click="toggleReadingGuide"
              :class="{ active: settings.readingGuide }"
              :aria-pressed="settings.readingGuide"
            >
              {{ $t('accessibility.readingGuide') }}
            </button>
            <button
              @click="toggleDyslexiaFont"
              :class="{ active: settings.dyslexiaFont }"
              :aria-pressed="settings.dyslexiaFont"
            >
              {{ $t('accessibility.dyslexiaFont') }}
            </button>
          </div>
        </div>

        <!-- Motion Controls -->
        <div class="control-group">
          <h3>{{ $t('accessibility.motion') }}</h3>
          <div class="button-group">
            <button
              @click="toggleReduceMotion"
              :class="{ active: settings.reduceMotion }"
              :aria-pressed="settings.reduceMotion"
            >
              {{ $t('accessibility.reduceMotion') }}
            </button>
            <button
              @click="togglePauseAnimations"
              :class="{ active: settings.pauseAnimations }"
              :aria-pressed="settings.pauseAnimations"
            >
              {{ $t('accessibility.pauseAnimations') }}
            </button>
          </div>
        </div>

        <!-- Reset Button -->
        <div class="control-group">
          <button @click="resetSettings" class="reset-btn">
            {{ $t('accessibility.reset') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Reading Guide -->
    <div
      v-if="settings.readingGuide"
      class="reading-guide"
      :style="{ top: readingGuidePosition + 'px' }"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const isOpen = ref(false)
const readingGuidePosition = ref(0)

const settings = reactive({
  fontSize: 100,
  highContrast: false,
  darkMode: false,
  readingGuide: false,
  dyslexiaFont: false,
  reduceMotion: false,
  pauseAnimations: false
})

const toggleWidget = () => {
  isOpen.value = !isOpen.value
}

const closeWidget = () => {
  isOpen.value = false
}

const increaseFontSize = () => {
  if (settings.fontSize < 150) {
    settings.fontSize += 10
    applyFontSize()
  }
}

const decreaseFontSize = () => {
  if (settings.fontSize > 80) {
    settings.fontSize -= 10
    applyFontSize()
  }
}

const resetFontSize = () => {
  settings.fontSize = 100
  applyFontSize()
}

const applyFontSize = () => {
  document.documentElement.style.fontSize = `${settings.fontSize}%`
  saveSettings()
}

const toggleHighContrast = () => {
  settings.highContrast = !settings.highContrast
  document.documentElement.classList.toggle('high-contrast', settings.highContrast)
  saveSettings()
}

const toggleDarkMode = () => {
  settings.darkMode = !settings.darkMode
  document.documentElement.classList.toggle('dark-mode', settings.darkMode)
  saveSettings()
}

const toggleReadingGuide = () => {
  settings.readingGuide = !settings.readingGuide
  if (settings.readingGuide) {
    document.addEventListener('mousemove', updateReadingGuide)
  } else {
    document.removeEventListener('mousemove', updateReadingGuide)
  }
  saveSettings()
}

const toggleDyslexiaFont = () => {
  settings.dyslexiaFont = !settings.dyslexiaFont
  document.documentElement.classList.toggle('dyslexia-font', settings.dyslexiaFont)
  saveSettings()
}

const toggleReduceMotion = () => {
  settings.reduceMotion = !settings.reduceMotion
  document.documentElement.classList.toggle('reduce-motion', settings.reduceMotion)
  saveSettings()
}

const togglePauseAnimations = () => {
  settings.pauseAnimations = !settings.pauseAnimations
  document.documentElement.classList.toggle('pause-animations', settings.pauseAnimations)
  saveSettings()
}

const updateReadingGuide = (event: MouseEvent) => {
  readingGuidePosition.value = event.clientY
}

const resetSettings = () => {
  settings.fontSize = 100
  settings.highContrast = false
  settings.darkMode = false
  settings.readingGuide = false
  settings.dyslexiaFont = false
  settings.reduceMotion = false
  settings.pauseAnimations = false

  document.documentElement.style.fontSize = '100%'
  document.documentElement.classList.remove('high-contrast', 'dark-mode', 'dyslexia-font', 'reduce-motion', 'pause-animations')
  document.removeEventListener('mousemove', updateReadingGuide)
  
  saveSettings()
}

const saveSettings = () => {
  localStorage.setItem('accessibility-settings', JSON.stringify(settings))
}

const loadSettings = () => {
  const saved = localStorage.getItem('accessibility-settings')
  if (saved) {
    const savedSettings = JSON.parse(saved)
    Object.assign(settings, savedSettings)
    
    // Apply saved settings
    if (settings.fontSize !== 100) applyFontSize()
    if (settings.highContrast) document.documentElement.classList.add('high-contrast')
    if (settings.darkMode) document.documentElement.classList.add('dark-mode')
    if (settings.dyslexiaFont) document.documentElement.classList.add('dyslexia-font')
    if (settings.reduceMotion) document.documentElement.classList.add('reduce-motion')
    if (settings.pauseAnimations) document.documentElement.classList.add('pause-animations')
    if (settings.readingGuide) document.addEventListener('mousemove', updateReadingGuide)
  }
}

onMounted(() => {
  loadSettings()
})

onUnmounted(() => {
  document.removeEventListener('mousemove', updateReadingGuide)
})
</script>

<style scoped>
.accessibility-widget {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  z-index: 1000;
}

.accessibility-toggle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.accessibility-toggle:hover {
  background: #0056b3;
  transform: scale(1.1);
}

.accessibility-panel {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  width: 300px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #ddd;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.panel-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.panel-content {
  padding: 16px;
}

.control-group {
  margin-bottom: 20px;
}

.control-group h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.button-group button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.button-group button:hover {
  background: #f5f5f5;
}

.button-group button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.reset-btn {
  width: 100%;
  padding: 10px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
}

.reset-btn:hover {
  background: #c82333;
}

.reading-guide {
  position: fixed;
  left: 0;
  right: 0;
  height: 2px;
  background: #ff0000;
  pointer-events: none;
  z-index: 999;
}
</style>

<style>
/* Global accessibility styles */
.high-contrast {
  filter: contrast(150%);
}

.dark-mode {
  filter: invert(1) hue-rotate(180deg);
}

.dyslexia-font * {
  font-family: 'OpenDyslexic', Arial, sans-serif !important;
}

.reduce-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

.pause-animations * {
  animation-play-state: paused !important;
}
</style>
