<template>
  <div
    v-if="showConsent"
    class="fixed bottom-0 left-0 right-0 z-50 bg-gray-900 text-white p-4 shadow-lg"
  >
    <div class="container mx-auto">
      <div class="flex flex-col md:flex-row items-center justify-between gap-4">
        <div class="flex-1">
          <h3 class="text-lg font-semibold mb-2">{{ $t('cookie.title') }}</h3>
          <p class="text-sm text-gray-300">
            {{ $t('cookie.description') }}
            <router-link to="/privacy" class="text-blue-400 hover:text-blue-300 underline">
              {{ $t('cookie.privacyPolicy') }}
            </router-link>
          </p>
        </div>
        
        <div class="flex gap-3">
          <button
            @click="acceptSelected"
            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md text-sm transition-colors"
          >
            {{ $t('cookie.acceptSelected') }}
          </button>
          <button
            @click="acceptAll"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm transition-colors"
          >
            {{ $t('cookie.acceptAll') }}
          </button>
          <button
            @click="showSettings = true"
            class="px-4 py-2 bg-transparent border border-gray-500 hover:border-gray-400 text-white rounded-md text-sm transition-colors"
          >
            {{ $t('cookie.settings') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Cookie Settings Modal -->
    <div
      v-if="showSettings"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60"
      @click.self="showSettings = false"
    >
      <div class="bg-white text-gray-900 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <h2 class="text-2xl font-bold mb-4">{{ $t('cookie.settingsTitle') }}</h2>
        
        <div class="space-y-4">
          <!-- Essential Cookies -->
          <div class="border-b pb-4">
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-lg font-semibold">{{ $t('cookie.essential') }}</h3>
              <input
                type="checkbox"
                checked
                disabled
                class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded"
              />
            </div>
            <p class="text-sm text-gray-600">{{ $t('cookie.essentialDesc') }}</p>
          </div>

          <!-- Analytics Cookies -->
          <div class="border-b pb-4">
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-lg font-semibold">{{ $t('cookie.analytics') }}</h3>
              <input
                v-model="cookieSettings.analytics"
                type="checkbox"
                class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded"
              />
            </div>
            <p class="text-sm text-gray-600">{{ $t('cookie.analyticsDesc') }}</p>
          </div>

          <!-- Marketing Cookies -->
          <div class="border-b pb-4">
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-lg font-semibold">{{ $t('cookie.marketing') }}</h3>
              <input
                v-model="cookieSettings.marketing"
                type="checkbox"
                class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded"
              />
            </div>
            <p class="text-sm text-gray-600">{{ $t('cookie.marketingDesc') }}</p>
          </div>

          <!-- Functional Cookies -->
          <div class="pb-4">
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-lg font-semibold">{{ $t('cookie.functional') }}</h3>
              <input
                v-model="cookieSettings.functional"
                type="checkbox"
                class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded"
              />
            </div>
            <p class="text-sm text-gray-600">{{ $t('cookie.functionalDesc') }}</p>
          </div>
        </div>

        <div class="flex justify-end gap-3 mt-6">
          <button
            @click="showSettings = false"
            class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-md text-sm transition-colors"
          >
            {{ $t('common.cancel') }}
          </button>
          <button
            @click="saveSettings"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm transition-colors"
          >
            {{ $t('common.save') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const showConsent = ref(false)
const showSettings = ref(false)

const cookieSettings = ref({
  essential: true,
  analytics: false,
  marketing: false,
  functional: false
})

const checkConsentStatus = () => {
  const consent = localStorage.getItem('cookie-consent')
  if (!consent) {
    showConsent.value = true
  } else {
    const settings = JSON.parse(consent)
    cookieSettings.value = { ...cookieSettings.value, ...settings }
    loadScripts()
  }
}

const acceptAll = () => {
  cookieSettings.value = {
    essential: true,
    analytics: true,
    marketing: true,
    functional: true
  }
  saveConsent()
}

const acceptSelected = () => {
  saveConsent()
}

const saveSettings = () => {
  saveConsent()
  showSettings.value = false
}

const saveConsent = () => {
  localStorage.setItem('cookie-consent', JSON.stringify(cookieSettings.value))
  localStorage.setItem('cookie-consent-date', new Date().toISOString())
  showConsent.value = false
  loadScripts()
}

const loadScripts = () => {
  // Load Google Analytics if analytics cookies are accepted
  if (cookieSettings.value.analytics) {
    loadGoogleAnalytics()
  }

  // Load other marketing scripts if marketing cookies are accepted
  if (cookieSettings.value.marketing) {
    loadMarketingScripts()
  }

  // Load functional scripts if functional cookies are accepted
  if (cookieSettings.value.functional) {
    loadFunctionalScripts()
  }
}

const loadGoogleAnalytics = () => {
  // Google Analytics implementation
  if (typeof window !== 'undefined' && !(window as any).gtag) {
    const script = document.createElement('script')
    script.async = true
    script.src = 'https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID'
    document.head.appendChild(script)

    script.onload = () => {
      ;(window as any).dataLayer = (window as any).dataLayer || []
      function gtag(...args: any[]) {
        ;(window as any).dataLayer.push(args)
      }
      ;(window as any).gtag = gtag
      gtag('js', new Date())
      gtag('config', 'GA_MEASUREMENT_ID')
    }
  }
}

const loadMarketingScripts = () => {
  // Load marketing/advertising scripts here
  console.log('Loading marketing scripts...')
}

const loadFunctionalScripts = () => {
  // Load functional scripts here (chat widgets, etc.)
  console.log('Loading functional scripts...')
}

onMounted(() => {
  checkConsentStatus()
})
</script>

<style scoped>
.z-60 {
  z-index: 60;
}
</style>
