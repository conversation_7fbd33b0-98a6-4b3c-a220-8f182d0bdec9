<template>
  <!-- Hero Section Start-->
  <div class="hero">
    <!-- Video Start -->
    <div class="hero-bg-video">
      <!-- Selfhosted Video Start -->
      <!-- <video autoplay muted loop id="myVideo"><source src="images/hero-bg-video.mp4" type="video/mp4"></video> -->
      <video autoplay muted loop id="myVideo">
        <source src="https://demo.awaikenthemes.com/assets/videos/artistic-video.mp4" type="video/mp4">
      </video>
      <!-- Selfhosted Video End -->

      <!-- Youtube Video Start -->
      <!-- <div id="herovideo" class="player" data-property="{videoURL:'74DWwSxsVSs',containment:'.hero-video', showControls:false, autoPlay:true, loop:true, vol:0, mute:false, startAt:0,  stopAt:296, opacity:1, addRaster:true, quality:'large', optimizeDisplay:true}"></div> -->
      <!-- Youtube Video End -->
    </div>
    <!-- Video End -->
    <div class="container">
      <div class="row">
        <div class="col-lg-12">
          <!-- Hero Content Start -->
          <div class="hero-content">
            <!-- Section Title Start -->
            <div class="section-title">
              <div class="typing-title">
                <p>{{ $t('hero.subtitle1') }}</p>
                <p>{{ $t('hero.subtitle2') }}</p>
                <p>{{ $t('hero.subtitle3') }}</p>
              </div>
              <h1 class="text-anime-style-2" data-cursor="-opaque">
                {{ $t('hero.title') }} <span class="typed-title"></span>
              </h1>
            </div>
            <!-- Section Title End -->

            <!-- Hero Content Body Start -->
            <div class="hero-content-body">
              <!-- Hero Content Video Start -->
              <div class="hero-content-video">
                <!-- Video Play Button Start -->
                <div class="video-play-button">
                  <a href="https://www.youtube.com/watch?v=Y-x0efG1seA" class="popup-video" data-cursor-text="Play">
                    <i class="fa-solid fa-play"></i>
                  </a>
                </div>
                <!-- Video Play Button End -->

                <!-- Learn More Circle Start -->
                <div class="learn-more-circle">
                  <img src="/images/learn-more-circle.svg" alt="">
                </div>
                <!-- Learn More Circle End -->
              </div>
              <!-- Hero Content Video End -->

              <!-- Hero Video Content Start -->
              <div class="hero-video-content wow fadeInUp">
                <p>{{ $t('hero.description') }}</p>
              </div>
              <!-- Hero Video Content End -->
            </div>
            <!-- Hero Content Body End -->

            <!-- Hero Button Start -->
            <div class="hero-btn wow fadeInUp" data-wow-delay="0.25s">
              <router-link to="/contact" class="btn-default">{{ $t('hero.getInTouch') }}</router-link>
            </div>
            <!-- Hero Button End -->
          </div>
          <!-- Hero Content End -->
        </div>
      </div>
    </div>
  </div>
  <!-- Hero Section End-->
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

let typedInstance: any = null

onMounted(() => {
  // Initialize animations after component mounts
  if (typeof window !== 'undefined') {
    // Load external scripts dynamically
    const loadScript = (src: string) => {
      return new Promise((resolve, reject) => {
        // Check if script already exists
        const existingScript = document.querySelector(`script[src="${src}"]`)
        if (existingScript) {
          resolve(existingScript)
          return
        }

        const script = document.createElement('script')
        script.src = src
        script.onload = resolve
        script.onerror = reject
        document.head.appendChild(script)
      })
    }

    // Load required scripts with error handling
    const scriptsToLoad = [
      { src: '/js/typed.js', required: false },
      { src: '/js/wow.js', required: false },
      { src: '/js/gsap.min.js', required: false }
    ]

    scriptsToLoad.forEach(({ src, required }) => {
      loadScript(src).then(() => {
        console.log(`Script loaded: ${src}`)

        // Initialize typed animation if typed.js is loaded
        if (src.includes('typed.js')) {
          setTimeout(() => {
            const typedElement = document.querySelector('.typed-title')
            const typingTitle = document.querySelector('.typing-title')

            if (typedElement && typingTitle && (window as any).Typed) {
              try {
                typedInstance = new (window as any).Typed('.typed-title', {
                  stringsElement: '.typing-title',
                  backDelay: 2000,
                  typeSpeed: 50,
                  backSpeed: 30,
                  loop: true
                })
              } catch (error) {
                console.warn('Typed.js initialization failed:', error)
              }
            }
          }, 100)
        }

        // Initialize WOW animations if wow.js is loaded
        if (src.includes('wow.js') && (window as any).WOW) {
          try {
            new (window as any).WOW().init()
          } catch (error) {
            console.warn('WOW.js initialization failed:', error)
          }
        }
      }).catch(error => {
        if (required) {
          console.error(`Required script failed to load: ${src}`, error)
        } else {
          console.warn(`Optional script failed to load: ${src}`, error)
        }
      })
    })
  }
})

onUnmounted(() => {
  // Cleanup typed instance
  if (typedInstance) {
    typedInstance.destroy()
  }
})
</script>

<style scoped>
/* Hero styles are handled by external CSS */
</style>
