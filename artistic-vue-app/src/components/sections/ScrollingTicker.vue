<template>
  <!-- Scrolling Ticker Start -->
  <div class="scrolling-ticker">
    <div class="scrolling-ticker-box">
      <div class="scrolling-ticker-content">
        <h3>{{ $t('ticker.text1') }}</h3>
        <div class="scrolling-ticker-icon">
          <img src="/images/scrolling-ticker-icon.svg" alt="">
        </div>
        <h3>{{ $t('ticker.text2') }}</h3>
        <div class="scrolling-ticker-icon">
          <img src="/images/scrolling-ticker-icon.svg" alt="">
        </div>
        <h3>{{ $t('ticker.text3') }}</h3>
        <div class="scrolling-ticker-icon">
          <img src="/images/scrolling-ticker-icon.svg" alt="">
        </div>
        <h3>{{ $t('ticker.text4') }}</h3>
        <div class="scrolling-ticker-icon">
          <img src="/images/scrolling-ticker-icon.svg" alt="">
        </div>
      </div>
    </div>
  </div>
  <!-- Scrolling Ticker End -->
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>

<style scoped>
/* Scrolling ticker styles are handled by external CSS */
</style>
