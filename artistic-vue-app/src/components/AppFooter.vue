<template>
  <!-- Footer Start -->
  <footer class="main-footer">
    <!-- Let's Work Together start -->
    <div class="footer-work-together">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div class="work-together-box">
              <!-- Work Together Content Start -->
              <div class="work-together-content">
                <h3>{{ $t('footer.letsCollaborate') }}</h3>
                <h2>{{ $t('footer.letsWorkTogether') }}</h2>
              </div>
              <!-- Work Together Content End -->
              
              <!-- Work Together Btn Start -->
              <div class="work-together-btn">
                <router-link to="/contact">
                  <img src="/images/arrow-dark.svg" alt="">
                </router-link>
              </div>
              <!-- Work Together Btn End -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Let's Work Together End -->

    <!-- Footer Main Start -->
    <div class="footer-main">
      <div class="container">
        <div class="row">
          <div class="col-lg-4 col-md-6">
            <!-- About Footer start -->
            <div class="about-footer">
              <!-- Footer Logo Start -->
              <div class="footer-logo">
                <img src="/images/footer-logo.svg" alt="">
              </div>
              <!-- Footer Logo End -->

              <!-- Footer Contact Box Start -->
              <div class="footer-contact-box">
                <!-- Footer Contact Item Start -->
                <div class="footer-contact-item">
                  <div class="icon-box">
                    <img src="/images/icon-phone.svg" alt="">
                  </div>
                  <div class="footer-contact-content">
                    <p>{{ contactInfo.phone }}</p>
                  </div>
                </div>
                <!-- Footer Contact Item End -->

                <!-- Footer Contact Item Start -->
                <div class="footer-contact-item">
                  <div class="icon-box">
                    <img src="/images/icon-mail.svg" alt="">
                  </div>
                  <div class="footer-contact-content">
                    <p>{{ contactInfo.email }}</p>
                  </div>
                </div>
                <!-- Footer Contact Item End -->
              </div>
              <!-- Footer Contact Box End -->
            </div>
            <!-- About Footer End -->
          </div>

          <div class="col-lg-2 col-md-3 col-6">
            <!-- Footer Links start -->
            <div class="footer-links">
              <h3>{{ $t('footer.quickLinks') }}</h3>
              <ul>
                <li><router-link to="/about">{{ $t('nav.about') }}</router-link></li>
                <li><router-link to="/services">{{ $t('nav.services') }}</router-link></li>
                <li><router-link to="/projects">{{ $t('nav.projects') }}</router-link></li>
                <li><router-link to="/contact">{{ $t('nav.contact') }}</router-link></li>
              </ul>
            </div>
            <!-- Footer Links End -->
          </div>

          <div class="col-lg-2 col-md-3 col-6">
            <!-- Footer Links start -->
            <div class="footer-links">
              <h3>{{ $t('footer.support') }}</h3>
              <ul>
                <li><router-link to="/faqs">{{ $t('nav.faqs') }}</router-link></li>
                <li><router-link to="/privacy">{{ $t('footer.privacy') }}</router-link></li>
                <li><router-link to="/terms">{{ $t('footer.terms') }}</router-link></li>
                <li><router-link to="/help">{{ $t('footer.help') }}</router-link></li>
              </ul>
            </div>
            <!-- Footer Links End -->
          </div>

          <div class="col-lg-4">
            <!-- Footer Newsletter Form Start -->
            <div class="footer-newsletter-form">
              <h3>{{ $t('footer.subscribeNewsletter') }}</h3>
              <form @submit.prevent="subscribeNewsletter">
                <div class="form-group">
                  <input 
                    type="email" 
                    v-model="newsletterEmail" 
                    class="form-control" 
                    :placeholder="$t('footer.enterEmail')" 
                    required
                  >
                  <button type="submit" class="btn-highlighted">{{ $t('footer.subscribe') }}</button>
                </div>
              </form>
            </div>
            <!-- Footer Newsletter Form End -->

            <!-- Footer Social Link Start -->
            <div class="footer-social-links">
              <ul>
                <li><a href="#"><i class="fa-brands fa-pinterest-p"></i></a></li>
                <li><a href="#"><i class="fa-brands fa-x-twitter"></i></a></li>
                <li><a href="#"><i class="fa-brands fa-facebook-f"></i></a></li>
                <li><a href="#"><i class="fa-brands fa-instagram"></i></a></li>
              </ul>
            </div>
            <!-- Footer Social Link End -->
          </div>
        </div>

        <!-- Footer Copyright Section Start -->
        <div class="footer-copyright">
          <div class="row align-items-center">                       
            <div class="col-lg-12">
              <!-- Footer Copyright Start -->
              <div class="footer-copyright-text">
                <p>{{ $t('footer.copyright', { year: currentYear }) }}</p>
              </div>
              <!-- Footer Copyright End -->
            </div>
          </div>
        </div>
        <!-- Footer Copyright Section End -->
      </div>
    </div>
    <!-- Footer Main End -->
  </footer>
  <!-- Footer End -->
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useSettingsStore } from '@/stores/settings'

const { t } = useI18n()
const settingsStore = useSettingsStore()

const newsletterEmail = ref('')
const currentYear = new Date().getFullYear()

const contactInfo = computed(() => settingsStore.contactInfo)

const subscribeNewsletter = () => {
  // Newsletter subscription logic
  console.log('Newsletter subscription:', newsletterEmail.value)
  // Add API call here
  newsletterEmail.value = ''
}
</script>

<style scoped>
/* Footer styles are handled by external CSS */
</style>
