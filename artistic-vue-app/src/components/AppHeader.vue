<template>
  <!-- Header Start -->
  <header class="main-header">
    <div class="header-sticky">
      <nav class="navbar navbar-expand-lg">
        <div class="container">
          <!-- Logo Start -->
          <router-link class="navbar-brand" to="/">
            <img src="/images/logo.svg" alt="Logo">
          </router-link>
          <!-- Logo End -->

          <!-- Main Menu Start -->
          <div class="collapse navbar-collapse main-menu">
            <div class="nav-menu-wrapper">
              <ul class="navbar-nav mr-auto" id="menu">
                <li class="nav-item submenu">
                  <router-link class="nav-link" to="/">{{ $t('nav.home') }}</router-link>
                  <ul>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/">{{ $t('nav.homeMain') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/home-image">{{ $t('nav.homeImage') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/home-slider">{{ $t('nav.homeSlider') }}</router-link>
                    </li>
                  </ul>
                </li>                                
                <li class="nav-item">
                  <router-link class="nav-link" to="/about">{{ $t('nav.about') }}</router-link>
                </li>
                <li class="nav-item">
                  <router-link class="nav-link" to="/services">{{ $t('nav.services') }}</router-link>
                </li>
                <li class="nav-item submenu">
                  <a class="nav-link" href="#">{{ $t('nav.pages') }}</a>
                  <ul>                                        
                    <li class="nav-item">
                      <router-link class="nav-link" to="/service-details">{{ $t('nav.serviceDetails') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/blog">{{ $t('nav.blog') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/blog-details">{{ $t('nav.blogDetails') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/projects">{{ $t('nav.projects') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/project-details">{{ $t('nav.projectDetails') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/team">{{ $t('nav.team') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/team-details">{{ $t('nav.teamDetails') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/pricing">{{ $t('nav.pricing') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/testimonials">{{ $t('nav.testimonials') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/gallery">{{ $t('nav.gallery') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/video-gallery">{{ $t('nav.videoGallery') }}</router-link>
                    </li>
                    <li class="nav-item">
                      <router-link class="nav-link" to="/faqs">{{ $t('nav.faqs') }}</router-link>
                    </li>
                  </ul>
                </li>
                <li class="nav-item">
                  <router-link class="nav-link" to="/contact">{{ $t('nav.contact') }}</router-link>
                </li>                             
              </ul>
            </div>

            <!-- Header Social Box Start -->
            <div class="header-social-box d-inline-flex">
              <!-- Header Social Links Start -->
              <div class="header-social-links">
                <ul>
                  <li><a href="#"><i class="fa-brands fa-x-twitter"></i></a></li>
                  <li><a href="#"><i class="fa-brands fa-facebook-f"></i></a></li>
                  <li><a href="#"><i class="fa-brands fa-instagram"></i></a></li>
                </ul>
              </div>
              <!-- Header Social Links End -->

              <!-- Header Btn Start -->
              <div class="header-btn">
                <!-- Toggle Button trigger modal Start -->
                <button class="btn btn-popup" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight" aria-controls="offcanvasRight">
                  <img src="/images/header-btn-dot.svg" alt="">
                </button>
                <!-- Toggle Button trigger modal End -->

                <!-- Header Sidebar Start -->
                <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight">
                  <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                  
                  <div class="offcanvas-body">
                    <!-- Header Contact Box Start -->
                    <div class="header-contact-box">
                      <div class="icon-box">
                        <img src="/images/icon-phone.svg" alt="">
                      </div>
                      <div class="header-contact-box-content">
                        <h3>{{ $t('contact.phone') }}</h3>
                        <p>{{ contactInfo.phone }}</p>
                      </div>
                    </div>
                    <!-- Header Contact Box End -->

                    <!-- Header Contact Box Start -->
                    <div class="header-contact-box">
                      <div class="icon-box">
                        <img src="/images/icon-mail.svg" alt="">
                      </div>
                      <div class="header-contact-box-content">
                        <h3>{{ $t('contact.email') }}</h3>
                        <p>{{ contactInfo.email }}</p>
                      </div>
                    </div>
                    <!-- Header Contact Box End -->

                    <!-- Header Contact Box Start -->
                    <div class="header-contact-box">
                      <div class="icon-box">
                        <img src="/images/icon-location.svg" alt="">
                      </div>
                      <div class="header-contact-box-content">
                        <h3>{{ $t('contact.address') }}</h3>
                        <p>{{ contactInfo.address }}</p>
                      </div>
                    </div>
                    <!-- Header Contact Box End -->

                    <!-- Header Social Links Start -->
                    <div class="header-social-links sidebar-social-links">
                      <h3>{{ $t('social.stayConnected') }}</h3>
                      <ul>
                        <li><a href="#"><i class="fa-brands fa-x-twitter"></i></a></li>
                        <li><a href="#"><i class="fa-brands fa-facebook-f"></i></a></li>
                        <li><a href="#"><i class="fa-brands fa-instagram"></i></a></li>
                      </ul>
                    </div>
                    <!-- Header Social Links End -->
                  </div>
                </div>
                <!-- Header Sidebar End -->
              </div>
              <!-- Header Btn End -->
            </div>
            <!-- Header Social Box End -->
          </div>
          <!-- Main Menu End -->
          <div class="navbar-toggle"></div>
        </div>
      </nav>
      <div class="responsive-menu"></div>
    </div>
  </header>
  <!-- Header End -->
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useSettingsStore } from '@/stores/settings'

const { t } = useI18n()
const settingsStore = useSettingsStore()

const contactInfo = computed(() => settingsStore.contactInfo)
</script>

<style scoped>
/* Header styles are handled by external CSS */
</style>
