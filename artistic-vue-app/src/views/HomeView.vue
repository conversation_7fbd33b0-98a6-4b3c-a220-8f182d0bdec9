<script setup lang="ts">
import { onMounted } from 'vue'
import HeroSection from '@/components/sections/HeroSection.vue'
import ScrollingTicker from '@/components/sections/ScrollingTicker.vue'
import AboutAgency from '@/components/sections/AboutAgency.vue'
import OurServices from '@/components/sections/OurServices.vue'
import DigitalSuccess from '@/components/sections/DigitalSuccess.vue'
import WhyChooseUs from '@/components/sections/WhyChooseUs.vue'
import JoinAgency from '@/components/sections/JoinAgency.vue'
import OurProjects from '@/components/sections/OurProjects.vue'
import OurTestimonial from '@/components/sections/OurTestimonial.vue'
import AgencyBenefits from '@/components/sections/AgencyBenefits.vue'
import OurBlog from '@/components/sections/OurBlog.vue'

onMounted(() => {
  // Initialize any required JavaScript functionality
  // This would include jQuery plugins, GSAP animations, etc.
})
</script>

<template>
  <main>
    <HeroSection />
    <ScrollingTicker />
    <AboutAgency />
    <OurServices />
    <DigitalSuccess />
    <WhyChooseUs />
    <JoinAgency />
    <OurProjects />
    <OurTestimonial />
    <AgencyBenefits />
    <OurBlog />
  </main>
</template>
