<template>
  <main>
    <div class="container">
      <div class="row">
        <div class="col-lg-12">
          <div class="section-title text-center">
            <h1>{{ $t('pages.comingSoon') }}</h1>
            <p>{{ $t('pages.underConstruction') }}</p>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>

<style scoped>
.section-title {
  padding: 100px 0;
}
</style>
