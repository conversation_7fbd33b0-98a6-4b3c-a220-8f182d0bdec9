<template>
  <div>
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Dashboard
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Welcome back, {{ authStore.userDisplayName }}!
        </p>
      </div>
      <div class="mt-4 flex md:mt-0 md:ml-4">
        <button
          type="button"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <ArrowPathIcon class="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>
    </div>

    <!-- Stats -->
    <div class="mt-8">
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div
          v-for="stat in stats"
          :key="stat.name"
          class="bg-white overflow-hidden shadow rounded-lg"
        >
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <component :is="stat.icon" class="h-6 w-6 text-gray-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    {{ stat.name }}
                  </dt>
                  <dd>
                    <div class="text-lg font-medium text-gray-900">
                      {{ stat.value }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <a :href="stat.href" class="font-medium text-blue-700 hover:text-blue-900">
                View all
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent activity -->
    <div class="mt-8">
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- Recent content -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Recent Content
            </h3>
            <div class="mt-5">
              <div v-if="recentContent.length === 0" class="text-sm text-gray-500">
                No content yet. <router-link to="/admin/content/create" class="text-blue-600 hover:text-blue-500">Create your first content</router-link>
              </div>
              <ul v-else class="divide-y divide-gray-200">
                <li
                  v-for="item in recentContent"
                  :key="item.id"
                  class="py-3 flex justify-between items-center"
                >
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      {{ item.title }}
                    </p>
                    <p class="text-sm text-gray-500">
                      {{ item.category }} • {{ formatDate(item.updatedAt) }}
                    </p>
                  </div>
                  <div class="flex-shrink-0">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="item.isPublished ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'"
                    >
                      {{ item.isPublished ? 'Published' : 'Draft' }}
                    </span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Quick actions -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Quick Actions
            </h3>
            <div class="mt-5 grid grid-cols-1 gap-3">
              <router-link
                v-for="action in quickActions"
                :key="action.name"
                :to="action.href"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                v-if="!action.requiresRole || authStore.hasPermission(action.requiresRole)"
              >
                <component :is="action.icon" class="w-4 h-4 mr-2" />
                {{ action.name }}
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- System status -->
    <div class="mt-8">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            System Status
          </h3>
          <div class="mt-5">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div
                v-for="status in systemStatus"
                :key="status.name"
                class="flex items-center"
              >
                <div
                  class="flex-shrink-0 w-3 h-3 rounded-full"
                  :class="status.status === 'healthy' ? 'bg-green-400' : status.status === 'warning' ? 'bg-yellow-400' : 'bg-red-400'"
                ></div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">{{ status.name }}</p>
                  <p class="text-sm text-gray-500">{{ status.message }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import {
  ArrowPathIcon,
  DocumentTextIcon,
  PhotoIcon,
  CogIcon,
  UsersIcon,
  PlusIcon,
  LanguageIcon
} from '@heroicons/vue/24/outline'

const authStore = useAuthStore()

const stats = ref([
  {
    name: 'Total Content',
    value: '0',
    icon: DocumentTextIcon,
    href: '/admin/content'
  },
  {
    name: 'Media Files',
    value: '0',
    icon: PhotoIcon,
    href: '/admin/media'
  },
  {
    name: 'Languages',
    value: '5',
    icon: LanguageIcon,
    href: '/admin/translations'
  },
  {
    name: 'Users',
    value: '1',
    icon: UsersIcon,
    href: '/admin/users'
  }
])

const recentContent = ref([
  // Mock data - will be replaced with real API data
])

const quickActions = [
  {
    name: 'Create Content',
    href: '/admin/content/create',
    icon: PlusIcon,
    requiresRole: 'editor'
  },
  {
    name: 'Upload Media',
    href: '/admin/media',
    icon: PhotoIcon,
    requiresRole: 'editor'
  },
  {
    name: 'Manage Settings',
    href: '/admin/settings',
    icon: CogIcon,
    requiresRole: 'admin'
  },
  {
    name: 'Manage Users',
    href: '/admin/users',
    icon: UsersIcon,
    requiresRole: 'admin'
  }
]

const systemStatus = ref([
  {
    name: 'API Server',
    status: 'healthy',
    message: 'All systems operational'
  },
  {
    name: 'Database',
    status: 'healthy',
    message: 'Connected'
  },
  {
    name: 'File Storage',
    status: 'healthy',
    message: 'Available'
  }
])

const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

onMounted(async () => {
  // Load dashboard data
  try {
    // This would be replaced with actual API calls
    console.log('Loading dashboard data...')
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
})
</script>

<style scoped>
/* Additional styles if needed */
</style>
