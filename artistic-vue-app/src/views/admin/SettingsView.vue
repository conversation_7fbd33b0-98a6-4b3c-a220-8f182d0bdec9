<template>
  <div>
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between mb-8">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Settings
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Manage your site configuration and integrations
        </p>
      </div>
      <div class="mt-4 flex md:mt-0 md:ml-4">
        <button
          @click="saveAllSettings"
          :disabled="isSaving"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <span v-if="isSaving" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </span>
          <span v-else>Save All Settings</span>
        </button>
      </div>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white shadow rounded-lg">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
              'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
            ]"
          >
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <div class="p-6">
        <!-- General Settings -->
        <div v-if="activeTab === 'general'" class="space-y-6">
          <h3 class="text-lg font-medium text-gray-900">General Settings</h3>

          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label class="block text-sm font-medium text-gray-700">Site Title</label>
              <input
                v-model="settings.site_title"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">Site Description</label>
              <textarea
                v-model="settings.site_description"
                rows="3"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              ></textarea>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">Contact Email</label>
              <input
                v-model="settings.contact_email"
                type="email"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">Contact Phone</label>
              <input
                v-model="settings.contact_phone"
                type="tel"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
        </div>

        <!-- Analytics Settings -->
        <div v-if="activeTab === 'analytics'" class="space-y-6">
          <h3 class="text-lg font-medium text-gray-900">Analytics & Tracking</h3>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">Google Analytics ID</label>
              <input
                v-model="settings.google_analytics_id"
                type="text"
                placeholder="G-XXXXXXXXXX"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
              <p class="mt-1 text-sm text-gray-500">Your Google Analytics 4 Measurement ID</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">Google Tag Manager ID</label>
              <input
                v-model="settings.google_tag_manager_id"
                type="text"
                placeholder="GTM-XXXXXXX"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
              <p class="mt-1 text-sm text-gray-500">Your Google Tag Manager Container ID</p>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-yellow-800">Analytics Setup</h3>
                  <div class="mt-2 text-sm text-yellow-700">
                    <p>If you use Google Tag Manager, you don't need to set Google Analytics ID separately. GTM can manage all your tracking codes.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Settings -->
        <div v-if="activeTab === 'ai'" class="space-y-6">
          <h3 class="text-lg font-medium text-gray-900">AI Integration</h3>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">AI Service Status</label>
              <div class="mt-1 flex items-center">
                <div class="flex-shrink-0 w-3 h-3 rounded-full bg-green-400"></div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">Pollinations AI Connected</p>
                  <p class="text-sm text-gray-500">Ready for content generation and translation</p>
                </div>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">Available Models</label>
              <div class="mt-1 grid grid-cols-2 gap-2">
                <div v-for="model in availableModels" :key="model" class="flex items-center">
                  <input
                    :id="model"
                    :value="model"
                    v-model="settings.ai_default_model"
                    type="radio"
                    class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
                  />
                  <label :for="model" class="ml-2 block text-sm text-gray-900">{{ model }}</label>
                </div>
              </div>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-blue-800">AI Features</h3>
                  <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                      <li>Automatic content translation</li>
                      <li>SEO optimization suggestions</li>
                      <li>Content generation assistance</li>
                      <li>Meta tags generation</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- SEO Settings -->
        <div v-if="activeTab === 'seo'" class="space-y-6">
          <h3 class="text-lg font-medium text-gray-900">SEO Configuration</h3>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">Default Meta Keywords</label>
              <input
                v-model="settings.default_keywords"
                type="text"
                placeholder="digital agency, web design, creative solutions"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
              <p class="mt-1 text-sm text-gray-500">Comma-separated keywords for pages without specific keywords</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">Default OG Image URL</label>
              <input
                v-model="settings.default_og_image"
                type="url"
                placeholder="https://yoursite.com/images/og-image.jpg"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
              <p class="mt-1 text-sm text-gray-500">Default Open Graph image for social sharing</p>
            </div>

            <div class="flex items-center">
              <input
                v-model="settings.auto_generate_seo"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label class="ml-2 block text-sm text-gray-900">
                Auto-generate SEO meta tags using AI
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="showSuccess" class="fixed bottom-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
      Settings saved successfully!
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { db } from '@/services/database'
import { analyticsService } from '@/services/analytics'
import { aiService } from '@/services/ai'

const activeTab = ref('general')
const isSaving = ref(false)
const showSuccess = ref(false)
const availableModels = ref(['gpt-4', 'gpt-3.5-turbo', 'claude-3', 'llama-2'])

const tabs = [
  { id: 'general', name: 'General' },
  { id: 'analytics', name: 'Analytics' },
  { id: 'ai', name: 'AI Integration' },
  { id: 'seo', name: 'SEO' }
]

const settings = ref({
  site_title: '',
  site_description: '',
  contact_email: '',
  contact_phone: '',
  google_analytics_id: '',
  google_tag_manager_id: '',
  ai_default_model: 'gpt-4',
  default_keywords: '',
  default_og_image: '',
  auto_generate_seo: false
})

const loadSettings = async () => {
  try {
    const allSettings = await db.getAllSettings()

    allSettings.forEach(setting => {
      if (setting.key in settings.value) {
        (settings.value as any)[setting.key] = setting.value
      }
    })
  } catch (error) {
    console.error('Failed to load settings:', error)
  }
}

const saveAllSettings = async () => {
  isSaving.value = true

  try {
    // Save all settings to database
    for (const [key, value] of Object.entries(settings.value)) {
      await db.updateSetting(key, value, 'admin')
    }

    // Update analytics configuration
    await analyticsService.updateAnalyticsConfig({
      googleAnalyticsId: settings.value.google_analytics_id,
      googleTagManagerId: settings.value.google_tag_manager_id
    })

    // Update AI configuration
    aiService.updateConfig({
      defaultModel: settings.value.ai_default_model
    })

    showSuccess.value = true
    setTimeout(() => {
      showSuccess.value = false
    }, 3000)

  } catch (error) {
    console.error('Failed to save settings:', error)
  } finally {
    isSaving.value = false
  }
}

const loadAvailableModels = async () => {
  try {
    const models = await aiService.getAvailableModels()
    availableModels.value = models
  } catch (error) {
    console.error('Failed to load AI models:', error)
  }
}

onMounted(async () => {
  await loadSettings()
  await loadAvailableModels()
})
</script>
