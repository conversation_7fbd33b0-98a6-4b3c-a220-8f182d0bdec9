import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { trackRouteChange } from '@/services/analytics'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/services',
      name: 'services',
      component: () => import('../views/ServicesView.vue'),
    },
    {
      path: '/contact',
      name: 'contact',
      component: () => import('../views/ContactView.vue'),
    },
    // Additional pages
    {
      path: '/home-image',
      name: 'home-image',
      component: () => import('../views/HomeImageView.vue'),
    },
    {
      path: '/home-slider',
      name: 'home-slider',
      component: () => import('../views/HomeSliderView.vue'),
    },
    {
      path: '/service-details',
      name: 'service-details',
      component: () => import('../views/ServiceDetailsView.vue'),
    },
    {
      path: '/blog',
      name: 'blog',
      component: () => import('../views/BlogView.vue'),
    },
    {
      path: '/blog-details',
      name: 'blog-details',
      component: () => import('../views/BlogDetailsView.vue'),
    },
    {
      path: '/projects',
      name: 'projects',
      component: () => import('../views/ProjectsView.vue'),
    },
    {
      path: '/project-details',
      name: 'project-details',
      component: () => import('../views/ProjectDetailsView.vue'),
    },
    {
      path: '/team',
      name: 'team',
      component: () => import('../views/TeamView.vue'),
    },
    {
      path: '/team-details',
      name: 'team-details',
      component: () => import('../views/TeamDetailsView.vue'),
    },
    {
      path: '/pricing',
      name: 'pricing',
      component: () => import('../views/PricingView.vue'),
    },
    {
      path: '/testimonials',
      name: 'testimonials',
      component: () => import('../views/TestimonialsView.vue'),
    },
    {
      path: '/gallery',
      name: 'gallery',
      component: () => import('../views/GalleryView.vue'),
    },
    {
      path: '/video-gallery',
      name: 'video-gallery',
      component: () => import('../views/VideoGalleryView.vue'),
    },
    {
      path: '/faqs',
      name: 'faqs',
      component: () => import('../views/FaqsView.vue'),
    },
    {
      path: '/privacy',
      name: 'privacy',
      component: () => import('../views/PrivacyView.vue'),
    },
    {
      path: '/terms',
      name: 'terms',
      component: () => import('../views/TermsView.vue'),
    },
    {
      path: '/help',
      name: 'help',
      component: () => import('../views/HelpView.vue'),
    },
    // Admin routes
    {
      path: '/admin',
      component: () => import('../layouts/AdminLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          redirect: '/admin/dashboard'
        },
        {
          path: 'dashboard',
          name: 'admin-dashboard',
          component: () => import('../views/admin/DashboardView.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'content',
          name: 'admin-content',
          component: () => import('../views/admin/ContentView.vue'),
          meta: { requiresAuth: true, requiresRole: 'editor' }
        },
        {
          path: 'content/create',
          name: 'admin-content-create',
          component: () => import('../views/admin/ContentCreateView.vue'),
          meta: { requiresAuth: true, requiresRole: 'editor' }
        },
        {
          path: 'content/:id/edit',
          name: 'admin-content-edit',
          component: () => import('../views/admin/ContentEditView.vue'),
          meta: { requiresAuth: true, requiresRole: 'editor' }
        },
        {
          path: 'media',
          name: 'admin-media',
          component: () => import('../views/admin/MediaView.vue'),
          meta: { requiresAuth: true, requiresRole: 'editor' }
        },
        {
          path: 'settings',
          name: 'admin-settings',
          component: () => import('../views/admin/SettingsView.vue'),
          meta: { requiresAuth: true, requiresRole: 'admin' }
        },
        {
          path: 'translations',
          name: 'admin-translations',
          component: () => import('../views/admin/TranslationsView.vue'),
          meta: { requiresAuth: true, requiresRole: 'editor' }
        },
        {
          path: 'users',
          name: 'admin-users',
          component: () => import('../views/admin/UsersView.vue'),
          meta: { requiresAuth: true, requiresRole: 'admin' }
        }
      ]
    },
    {
      path: '/admin/login',
      name: 'admin-login',
      component: () => import('../views/admin/LoginView.vue'),
      meta: { requiresGuest: true }
    }
  ],
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Initialize auth if not already done
  if (!authStore.isAuthenticated && localStorage.getItem('auth_token')) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.error('Auth initialization failed:', error)
    }
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/admin/login')
    return
  }

  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/admin/dashboard')
    return
  }

  // Check role-based access
  if (to.meta.requiresRole && !authStore.hasPermission(to.meta.requiresRole as any)) {
    next('/admin/dashboard') // Redirect to dashboard if insufficient permissions
    return
  }

  next()
})

// Track page views for analytics
router.afterEach((to, from) => {
  trackRouteChange(to, from)
})

export default router
