<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import AppHeader from './components/AppHeader.vue'
import AppFooter from './components/AppFooter.vue'
import CookieConsent from './components/common/CookieConsent.vue'
import AccessibilityWidget from './components/common/AccessibilityWidget.vue'

onMounted(() => {
  // Load external CSS files
  const cssFiles = [
    '/css/bootstrap.min.css',
    '/css/all.css',
    '/css/animate.css',
    '/css/swiper-bundle.min.css',
    '/css/magnific-popup.css',
    '/css/mousecursor.css',
    '/css/slicknav.min.css',
    '/css/custom.css'
  ]

  cssFiles.forEach(href => {
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = href
    document.head.appendChild(link)
  })
})
</script>

<template>
  <div id="app">
    <!-- Preloader Start -->
    <div class="preloader">
      <div class="loading-container">
        <div class="loading"></div>
        <div id="loading-icon"><img src="/images/loader.svg" alt=""></div>
      </div>
    </div>
    <!-- Preloader End -->

    <AppHeader />
    <RouterView />
    <AppFooter />
    <CookieConsent />
    <AccessibilityWidget />
  </div>
</template>

<style>
/* Global styles will be loaded from external CSS files */
</style>
