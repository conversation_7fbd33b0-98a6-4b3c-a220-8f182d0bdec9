<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted, ref } from 'vue'
import AppHeader from './components/AppHeader.vue'
import AppFooter from './components/AppFooter.vue'
import CookieConsent from './components/common/CookieConsent.vue'
import AccessibilityWidget from './components/common/AccessibilityWidget.vue'
import { db } from './services/database'

const isLoading = ref(true)

onMounted(async () => {
  try {
    // Wait for database to initialize
    await db.waitForInitialization()

    // Load external CSS files
    const cssFiles = [
      '/css/bootstrap.min.css',
      '/css/all.css',
      '/css/animate.css',
      '/css/swiper-bundle.min.css',
      '/css/magnific-popup.css',
      '/css/mousecursor.css',
      '/css/slicknav.min.css',
      '/css/custom.css'
    ]

    cssFiles.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = href
      document.head.appendChild(link)
    })

    // Small delay to ensure everything is loaded
    await new Promise(resolve => setTimeout(resolve, 500))

  } catch (error) {
    console.error('App initialization error:', error)
  } finally {
    isLoading.value = false
  }
})
</script>

<template>
  <div id="app">
    <!-- Loading Screen -->
    <div v-if="isLoading" class="preloader" style="display: flex;">
      <div class="loading-container">
        <div class="loading"></div>
        <div id="loading-icon"><img src="/images/loader.svg" alt="Loading..."></div>
        <p style="color: #333; margin-top: 20px; text-align: center;">Initializing application...</p>
      </div>
    </div>

    <!-- Main App Content -->
    <div v-else>
      <AppHeader />
      <RouterView />
      <AppFooter />
      <CookieConsent />
      <AccessibilityWidget />
    </div>
  </div>
</template>

<style>
/* Global styles will be loaded from external CSS files */
</style>
