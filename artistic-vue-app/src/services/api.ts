import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api'

class ApiService {
  private api: any

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user')
          window.location.href = '/admin/login'
        }
        return Promise.reject(error)
      }
    )
  }

  // Auth methods
  async login(email: string, password: string) {
    const response = await this.api.post('/auth/login', { email, password })
    return response.data
  }

  async register(userData: any) {
    const response = await this.api.post('/auth/register', userData)
    return response.data
  }

  async getCurrentUser() {
    const response = await this.api.get('/auth/me')
    return response.data
  }

  async logout() {
    const response = await this.api.post('/auth/logout')
    return response.data
  }

  // Content methods
  async getContent(params?: any) {
    const response = await this.api.get('/content', { params })
    return response.data
  }

  async getPublicContent(params?: any) {
    const response = await this.api.get('/content/public', { params })
    return response.data
  }

  async getContentById(id: string) {
    const response = await this.api.get(`/content/${id}`)
    return response.data
  }

  async createContent(contentData: any) {
    const response = await this.api.post('/content', contentData)
    return response.data
  }

  async updateContent(id: string, contentData: any) {
    const response = await this.api.put(`/content/${id}`, contentData)
    return response.data
  }

  async deleteContent(id: string) {
    const response = await this.api.delete(`/content/${id}`)
    return response.data
  }

  async getContentCategories() {
    const response = await this.api.get('/content/meta/categories')
    return response.data
  }

  // Settings methods
  async getSettings(category?: string) {
    const response = await this.api.get('/settings', { params: { category } })
    return response.data
  }

  async getPublicSettings(category?: string) {
    const response = await this.api.get('/settings/public', { params: { category } })
    return response.data
  }

  async getSettingById(id: string) {
    const response = await this.api.get(`/settings/${id}`)
    return response.data
  }

  async createSetting(settingData: any) {
    const response = await this.api.post('/settings', settingData)
    return response.data
  }

  async updateSetting(id: string, settingData: any) {
    const response = await this.api.put(`/settings/${id}`, settingData)
    return response.data
  }

  async bulkUpdateSettings(settings: any[]) {
    const response = await this.api.put('/settings/bulk/update', { settings })
    return response.data
  }

  async deleteSetting(id: string) {
    const response = await this.api.delete(`/settings/${id}`)
    return response.data
  }

  // Media methods
  async uploadFile(file: File, category = 'general') {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', category)

    const response = await this.api.post('/media/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  async uploadMultipleFiles(files: File[], category = 'general') {
    const formData = new FormData()
    files.forEach(file => formData.append('files', file))
    formData.append('category', category)

    const response = await this.api.post('/media/upload-multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  async getFiles(params?: any) {
    const response = await this.api.get('/media/files', { params })
    return response.data
  }

  async deleteFile(category: string, filename: string) {
    const response = await this.api.delete(`/media/files/${category}/${filename}`)
    return response.data
  }

  // Translation methods
  async translateText(text: string, targetLanguage: string, sourceLanguage = 'en', context = '') {
    const response = await this.api.post('/translations/translate', {
      text,
      targetLanguage,
      sourceLanguage,
      context
    })
    return response.data
  }

  async batchTranslate(texts: any[], targetLanguage: string, sourceLanguage = 'en') {
    const response = await this.api.post('/translations/translate-batch', {
      texts,
      targetLanguage,
      sourceLanguage
    })
    return response.data
  }

  async getSupportedLanguages() {
    const response = await this.api.get('/translations/languages')
    return response.data
  }

  async autoTranslateContent(language: string, contentIds: string[]) {
    const response = await this.api.post(`/translations/auto-translate/${language}`, {
      contentIds
    })
    return response.data
  }

  async generateSEO(title: string, description?: string, keywords?: string[], language = 'en') {
    const response = await this.api.post('/translations/generate-seo', {
      title,
      description,
      keywords,
      language
    })
    return response.data
  }

  // Health check
  async healthCheck() {
    const response = await this.api.get('/health')
    return response.data
  }
}

export default new ApiService()
