import { db } from './database'

interface AnalyticsConfig {
  googleAnalyticsId?: string
  googleTagManagerId?: string
  enabled: boolean
}

class AnalyticsService {
  private config: AnalyticsConfig = {
    enabled: false
  }

  async initialize() {
    // Load analytics configuration from database
    const gaIdSetting = await db.getSetting('google_analytics_id')
    const gtmIdSetting = await db.getSetting('google_tag_manager_id')
    
    this.config = {
      googleAnalyticsId: gaIdSetting?.value || '',
      googleTagManagerId: gtmIdSetting?.value || '',
      enabled: !!(gaIdSetting?.value || gtmIdSetting?.value)
    }

    if (this.config.enabled) {
      this.loadAnalyticsScripts()
    }
  }

  private loadAnalyticsScripts() {
    // Load Google Tag Manager
    if (this.config.googleTagManagerId) {
      this.loadGTM()
    }

    // Load Google Analytics (if no GTM)
    if (this.config.googleAnalyticsId && !this.config.googleTagManagerId) {
      this.loadGA()
    }
  }

  private loadGTM() {
    if (!this.config.googleTagManagerId) return

    // GTM Script
    const gtmScript = document.createElement('script')
    gtmScript.async = true
    gtmScript.src = `https://www.googletagmanager.com/gtm.js?id=${this.config.googleTagManagerId}`
    document.head.appendChild(gtmScript)

    // GTM DataLayer
    ;(window as any).dataLayer = (window as any).dataLayer || []
    ;(window as any).dataLayer.push({
      'gtm.start': new Date().getTime(),
      event: 'gtm.js'
    })

    // GTM NoScript (for body)
    this.addGTMNoScript()
  }

  private addGTMNoScript() {
    if (!this.config.googleTagManagerId) return

    const noscript = document.createElement('noscript')
    const iframe = document.createElement('iframe')
    iframe.src = `https://www.googletagmanager.com/ns.html?id=${this.config.googleTagManagerId}`
    iframe.height = '0'
    iframe.width = '0'
    iframe.style.display = 'none'
    iframe.style.visibility = 'hidden'
    noscript.appendChild(iframe)
    
    // Add to body when it's available
    if (document.body) {
      document.body.insertBefore(noscript, document.body.firstChild)
    } else {
      document.addEventListener('DOMContentLoaded', () => {
        document.body.insertBefore(noscript, document.body.firstChild)
      })
    }
  }

  private loadGA() {
    if (!this.config.googleAnalyticsId) return

    // Google Analytics Script
    const gaScript = document.createElement('script')
    gaScript.async = true
    gaScript.src = `https://www.googletagmanager.com/gtag/js?id=${this.config.googleAnalyticsId}`
    document.head.appendChild(gaScript)

    // Initialize gtag
    ;(window as any).dataLayer = (window as any).dataLayer || []
    function gtag(...args: any[]) {
      ;(window as any).dataLayer.push(args)
    }
    ;(window as any).gtag = gtag

    gtag('js', new Date())
    gtag('config', this.config.googleAnalyticsId, {
      page_title: document.title,
      page_location: window.location.href
    })
  }

  // Track page views
  trackPageView(path: string, title?: string) {
    if (!this.config.enabled) return

    if ((window as any).gtag) {
      ;(window as any).gtag('config', this.config.googleAnalyticsId, {
        page_path: path,
        page_title: title || document.title
      })
    }

    if ((window as any).dataLayer) {
      ;(window as any).dataLayer.push({
        event: 'page_view',
        page_path: path,
        page_title: title || document.title
      })
    }
  }

  // Track events
  trackEvent(eventName: string, parameters: Record<string, any> = {}) {
    if (!this.config.enabled) return

    if ((window as any).gtag) {
      ;(window as any).gtag('event', eventName, parameters)
    }

    if ((window as any).dataLayer) {
      ;(window as any).dataLayer.push({
        event: eventName,
        ...parameters
      })
    }
  }

  // Track custom events
  trackButtonClick(buttonName: string, location?: string) {
    this.trackEvent('button_click', {
      button_name: buttonName,
      location: location || window.location.pathname
    })
  }

  trackFormSubmit(formName: string, success: boolean = true) {
    this.trackEvent('form_submit', {
      form_name: formName,
      success: success
    })
  }

  trackDownload(fileName: string, fileType?: string) {
    this.trackEvent('file_download', {
      file_name: fileName,
      file_type: fileType
    })
  }

  trackSearch(searchTerm: string, resultsCount?: number) {
    this.trackEvent('search', {
      search_term: searchTerm,
      results_count: resultsCount
    })
  }

  trackVideoPlay(videoTitle: string, videoDuration?: number) {
    this.trackEvent('video_play', {
      video_title: videoTitle,
      video_duration: videoDuration
    })
  }

  trackSocialShare(platform: string, url?: string) {
    this.trackEvent('social_share', {
      platform: platform,
      url: url || window.location.href
    })
  }

  // E-commerce tracking
  trackPurchase(transactionId: string, value: number, currency: string = 'USD', items: any[] = []) {
    this.trackEvent('purchase', {
      transaction_id: transactionId,
      value: value,
      currency: currency,
      items: items
    })
  }

  trackAddToCart(itemId: string, itemName: string, value: number, currency: string = 'USD') {
    this.trackEvent('add_to_cart', {
      currency: currency,
      value: value,
      items: [{
        item_id: itemId,
        item_name: itemName,
        price: value,
        quantity: 1
      }]
    })
  }

  // User engagement
  trackUserEngagement(engagementTime: number) {
    this.trackEvent('user_engagement', {
      engagement_time_msec: engagementTime
    })
  }

  trackScrollDepth(scrollDepth: number) {
    this.trackEvent('scroll', {
      percent_scrolled: scrollDepth
    })
  }

  // Admin functions
  async updateAnalyticsConfig(config: Partial<AnalyticsConfig>) {
    if (config.googleAnalyticsId !== undefined) {
      await db.updateSetting('google_analytics_id', config.googleAnalyticsId, 'system')
    }
    
    if (config.googleTagManagerId !== undefined) {
      await db.updateSetting('google_tag_manager_id', config.googleTagManagerId, 'system')
    }

    // Reinitialize with new config
    await this.initialize()
  }

  getConfig() {
    return { ...this.config }
  }

  // Privacy compliance
  setConsentGranted() {
    if ((window as any).gtag) {
      ;(window as any).gtag('consent', 'update', {
        analytics_storage: 'granted',
        ad_storage: 'granted'
      })
    }
  }

  setConsentDenied() {
    if ((window as any).gtag) {
      ;(window as any).gtag('consent', 'update', {
        analytics_storage: 'denied',
        ad_storage: 'denied'
      })
    }
  }
}

export const analyticsService = new AnalyticsService()

// Vue Router integration helper
export const trackRouteChange = (to: any, from: any) => {
  analyticsService.trackPageView(to.path, to.meta?.title || to.name)
}

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  analyticsService.initialize()
}
