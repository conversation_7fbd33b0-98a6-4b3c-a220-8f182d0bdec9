interface AIConfig {
  apiUrl: string
  token: string
  defaultModel: string
}

interface TranslationRequest {
  text: string
  targetLanguage: string
  sourceLanguage?: string
  context?: string
}

interface SEORequest {
  content: string
  targetKeywords?: string[]
  language?: string
  type: 'title' | 'description' | 'keywords' | 'content'
}

interface ContentRequest {
  prompt: string
  type: 'blog' | 'product' | 'service' | 'about' | 'general'
  language?: string
  tone?: 'professional' | 'casual' | 'friendly' | 'formal'
  length?: 'short' | 'medium' | 'long'
}

class AIService {
  private config: AIConfig = {
    apiUrl: 'https://text.pollinations.ai/openai',
    token: 'BRn5JtxfwX4SLhdU',
    defaultModel: 'gpt-4'
  }

  private async makeRequest(prompt: string, model?: string): Promise<string> {
    try {
      const response = await fetch(`${this.config.apiUrl}?token=${this.config.token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.token}`
        },
        body: JSON.stringify({
          model: model || this.config.defaultModel,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 2000,
          temperature: 0.7
        })
      })

      if (!response.ok) {
        throw new Error(`AI API request failed: ${response.status}`)
      }

      const data = await response.json()
      return data.choices?.[0]?.message?.content || data.text || ''
    } catch (error) {
      console.error('AI Service Error:', error)
      throw new Error('Failed to get AI response')
    }
  }

  // Translation Services
  async translateText(request: TranslationRequest): Promise<string> {
    const { text, targetLanguage, sourceLanguage, context } = request
    
    const prompt = `
Translate the following text to ${targetLanguage}${sourceLanguage ? ` from ${sourceLanguage}` : ''}:

${context ? `Context: ${context}\n` : ''}
Text to translate: "${text}"

Requirements:
- Maintain the original meaning and tone
- Use natural, fluent language
- Preserve any formatting or special characters
- If it's technical content, use appropriate terminology
- Return only the translated text, no explanations

Translation:
    `.trim()

    return await this.makeRequest(prompt)
  }

  async translateBulk(texts: string[], targetLanguage: string, sourceLanguage?: string): Promise<string[]> {
    const translations: string[] = []
    
    for (const text of texts) {
      try {
        const translation = await this.translateText({
          text,
          targetLanguage,
          sourceLanguage
        })
        translations.push(translation)
        
        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100))
      } catch (error) {
        console.error(`Translation failed for text: ${text}`, error)
        translations.push(text) // Fallback to original text
      }
    }
    
    return translations
  }

  // SEO Services
  async generateSEOTitle(request: SEORequest): Promise<string> {
    const { content, targetKeywords, language = 'English' } = request
    
    const prompt = `
Generate an SEO-optimized title in ${language} for the following content:

Content: "${content}"
${targetKeywords ? `Target Keywords: ${targetKeywords.join(', ')}` : ''}

Requirements:
- Maximum 60 characters
- Include primary keyword naturally
- Make it compelling and click-worthy
- Ensure it accurately represents the content
- Use title case
- Return only the title, no explanations

SEO Title:
    `.trim()

    return await this.makeRequest(prompt)
  }

  async generateSEODescription(request: SEORequest): Promise<string> {
    const { content, targetKeywords, language = 'English' } = request
    
    const prompt = `
Generate an SEO-optimized meta description in ${language} for the following content:

Content: "${content}"
${targetKeywords ? `Target Keywords: ${targetKeywords.join(', ')}` : ''}

Requirements:
- Maximum 160 characters
- Include primary keyword naturally
- Make it compelling and informative
- Include a call-to-action if appropriate
- Accurately summarize the content
- Return only the description, no explanations

Meta Description:
    `.trim()

    return await this.makeRequest(prompt)
  }

  async generateSEOKeywords(request: SEORequest): Promise<string[]> {
    const { content, language = 'English' } = request
    
    const prompt = `
Generate SEO keywords in ${language} for the following content:

Content: "${content}"

Requirements:
- Generate 10-15 relevant keywords
- Include both short-tail and long-tail keywords
- Focus on search intent
- Include variations and synonyms
- Return as comma-separated list
- No explanations, just the keywords

Keywords:
    `.trim()

    const response = await this.makeRequest(prompt)
    return response.split(',').map(keyword => keyword.trim()).filter(keyword => keyword.length > 0)
  }

  async optimizeContentForSEO(request: SEORequest): Promise<string> {
    const { content, targetKeywords, language = 'English' } = request
    
    const prompt = `
Optimize the following content for SEO in ${language}:

Original Content: "${content}"
${targetKeywords ? `Target Keywords: ${targetKeywords.join(', ')}` : ''}

Requirements:
- Maintain the original meaning and tone
- Naturally incorporate target keywords
- Improve readability and structure
- Add relevant subheadings if needed
- Ensure keyword density is appropriate (1-2%)
- Keep the content engaging and valuable
- Return only the optimized content

Optimized Content:
    `.trim()

    return await this.makeRequest(prompt)
  }

  // Content Generation Services
  async generateContent(request: ContentRequest): Promise<string> {
    const { prompt, type, language = 'English', tone = 'professional', length = 'medium' } = request
    
    const lengthGuide = {
      short: '100-200 words',
      medium: '300-500 words',
      long: '800-1200 words'
    }
    
    const aiPrompt = `
Generate ${type} content in ${language} with a ${tone} tone.

Topic/Prompt: "${prompt}"
Length: ${lengthGuide[length]}
Type: ${type}

Requirements:
- Write in ${language}
- Use a ${tone} tone throughout
- Make it engaging and informative
- Include relevant details and examples
- Structure with appropriate headings if needed
- Ensure the content is original and valuable
- Target length: ${lengthGuide[length]}

Content:
    `.trim()

    return await this.makeRequest(aiPrompt)
  }

  async generateBlogPost(title: string, keywords?: string[], language: string = 'English'): Promise<{
    title: string
    content: string
    excerpt: string
    tags: string[]
  }> {
    const prompt = `
Write a comprehensive blog post in ${language}:

Title: "${title}"
${keywords ? `Keywords to include: ${keywords.join(', ')}` : ''}

Requirements:
- Write 800-1200 words
- Include introduction, main content, and conclusion
- Use subheadings to structure the content
- Make it engaging and informative
- Include practical tips or examples
- Write in a professional but accessible tone
- Naturally incorporate the keywords

Format the response as:
TITLE: [blog title]
EXCERPT: [2-3 sentence summary]
TAGS: [comma-separated relevant tags]
CONTENT: [full blog post content]
    `.trim()

    const response = await this.makeRequest(prompt)
    
    // Parse the response
    const lines = response.split('\n')
    let title_line = '', excerpt_line = '', tags_line = '', content_lines: string[] = []
    let currentSection = ''
    
    for (const line of lines) {
      if (line.startsWith('TITLE:')) {
        title_line = line.replace('TITLE:', '').trim()
        currentSection = 'title'
      } else if (line.startsWith('EXCERPT:')) {
        excerpt_line = line.replace('EXCERPT:', '').trim()
        currentSection = 'excerpt'
      } else if (line.startsWith('TAGS:')) {
        tags_line = line.replace('TAGS:', '').trim()
        currentSection = 'tags'
      } else if (line.startsWith('CONTENT:')) {
        currentSection = 'content'
      } else if (currentSection === 'content') {
        content_lines.push(line)
      }
    }
    
    return {
      title: title_line || title,
      content: content_lines.join('\n').trim(),
      excerpt: excerpt_line,
      tags: tags_line.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    }
  }

  // Utility Methods
  async improveText(text: string, improvements: string[] = ['grammar', 'clarity', 'engagement']): Promise<string> {
    const prompt = `
Improve the following text by focusing on: ${improvements.join(', ')}

Original Text: "${text}"

Requirements:
- Fix any grammar or spelling errors
- Improve clarity and readability
- Make it more engaging if needed
- Maintain the original meaning and tone
- Return only the improved text

Improved Text:
    `.trim()

    return await this.makeRequest(prompt)
  }

  async summarizeText(text: string, maxLength: number = 150): Promise<string> {
    const prompt = `
Summarize the following text in maximum ${maxLength} characters:

Text: "${text}"

Requirements:
- Keep it under ${maxLength} characters
- Maintain key information
- Make it clear and concise
- Return only the summary

Summary:
    `.trim()

    return await this.makeRequest(prompt)
  }

  // Available Models
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await fetch('https://text.pollinations.ai/models')
      const models = await response.json()
      return models || ['gpt-4', 'gpt-3.5-turbo', 'claude-3', 'llama-2']
    } catch (error) {
      console.error('Failed to fetch models:', error)
      return ['gpt-4', 'gpt-3.5-turbo']
    }
  }

  // Configuration
  updateConfig(newConfig: Partial<AIConfig>) {
    this.config = { ...this.config, ...newConfig }
  }

  getConfig(): AIConfig {
    return { ...this.config }
  }
}

export const aiService = new AIService()
export type { TranslationRequest, SEORequest, ContentRequest }
