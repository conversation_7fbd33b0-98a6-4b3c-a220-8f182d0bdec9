import Loki from 'lokijs'

interface User {
  id?: string
  email: string
  password: string
  role: 'admin' | 'editor' | 'viewer'
  name: string
  createdAt: Date
  updatedAt: Date
}

interface Content {
  id?: string
  title: string
  content: string
  type: 'text' | 'html' | 'image' | 'video'
  category: string
  language: string
  isPublished: boolean
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string
  createdAt: Date
  updatedAt: Date
  createdBy: string
}

interface Settings {
  id?: string
  key: string
  value: any
  category: string
  updatedAt: Date
  updatedBy: string
}

interface Media {
  id?: string
  filename: string
  originalName: string
  mimetype: string
  size: number
  path: string
  url: string
  uploadedAt: Date
  uploadedBy: string
}

class DatabaseService {
  private db: Loki
  private users: Collection<User> | null = null
  private content: Collection<Content> | null = null
  private settings: Collection<Settings> | null = null
  private media: Collection<Media> | null = null
  private isInitialized: boolean = false
  private initPromise: Promise<void> | null = null

  constructor() {
    this.initPromise = this.initialize()
  }

  private async initialize(): Promise<void> {
    return new Promise((resolve) => {
      this.db = new Loki('artistic-cms.db', {
        autoload: true,
        autoloadCallback: () => {
          this.initializeDatabase()
          this.isInitialized = true
          resolve()
        },
        autosave: true,
        autosaveInterval: 4000
      })
    })
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized && this.initPromise) {
      await this.initPromise
    }
  }

  // Public method to ensure initialization
  async waitForInitialization(): Promise<void> {
    return this.ensureInitialized()
  }

  private initializeDatabase() {
    // Initialize collections
    this.users = this.db.getCollection('users') || this.db.addCollection('users', {
      unique: ['email'],
      indices: ['email', 'role']
    })

    this.content = this.db.getCollection('content') || this.db.addCollection('content', {
      indices: ['category', 'language', 'isPublished', 'type']
    })

    this.settings = this.db.getCollection('settings') || this.db.addCollection('settings', {
      unique: ['key'],
      indices: ['category']
    })

    this.media = this.db.getCollection('media') || this.db.addCollection('media', {
      indices: ['mimetype', 'uploadedBy']
    })

    // Create default admin user if no users exist
    if (this.users.count() === 0) {
      this.createDefaultUsers()
    }

    // Create default settings if none exist
    if (this.settings.count() === 0) {
      this.createDefaultSettings()
    }
  }

  private createDefaultUsers() {
    const defaultUsers = [
      {
        email: '<EMAIL>',
        password: 'admin123', // In production, this should be hashed
        role: 'admin' as const,
        name: 'Admin User',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        password: 'editor123',
        role: 'editor' as const,
        name: 'Editor User',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    defaultUsers.forEach(user => this.users.insert(user))
  }

  private createDefaultSettings() {
    const defaultSettings = [
      {
        key: 'site_title',
        value: 'Artistic - Creative Digital Agency',
        category: 'general',
        updatedAt: new Date(),
        updatedBy: 'system'
      },
      {
        key: 'site_description',
        value: 'Professional digital agency providing creative solutions',
        category: 'general',
        updatedAt: new Date(),
        updatedBy: 'system'
      },
      {
        key: 'contact_email',
        value: '<EMAIL>',
        category: 'contact',
        updatedAt: new Date(),
        updatedBy: 'system'
      },
      {
        key: 'contact_phone',
        value: '+****************',
        category: 'contact',
        updatedAt: new Date(),
        updatedBy: 'system'
      },
      {
        key: 'google_analytics_id',
        value: '',
        category: 'analytics',
        updatedAt: new Date(),
        updatedBy: 'system'
      },
      {
        key: 'google_tag_manager_id',
        value: '',
        category: 'analytics',
        updatedAt: new Date(),
        updatedBy: 'system'
      }
    ]

    defaultSettings.forEach(setting => this.settings.insert(setting))
  }

  // User methods
  async findUserByEmail(email: string): Promise<User | null> {
    await this.ensureInitialized()
    if (!this.users) return null
    return this.users.findOne({ email })
  }

  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const user = {
      ...userData,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    return this.users.insert(user)
  }

  async getAllUsers(): Promise<User[]> {
    return this.users.find()
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    const user = this.users.findOne({ $loki: parseInt(id) })
    if (user) {
      Object.assign(user, updates, { updatedAt: new Date() })
      this.users.update(user)
      return user
    }
    return null
  }

  async deleteUser(id: string): Promise<boolean> {
    const user = this.users.findOne({ $loki: parseInt(id) })
    if (user) {
      this.users.remove(user)
      return true
    }
    return false
  }

  // Content methods
  async createContent(contentData: Omit<Content, 'id' | 'createdAt' | 'updatedAt'>): Promise<Content> {
    const content = {
      ...contentData,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    return this.content.insert(content)
  }

  async getAllContent(filters?: { category?: string; language?: string; isPublished?: boolean }): Promise<Content[]> {
    if (filters) {
      return this.content.find(filters)
    }
    return this.content.find()
  }

  async getContentById(id: string): Promise<Content | null> {
    return this.content.findOne({ $loki: parseInt(id) })
  }

  async updateContent(id: string, updates: Partial<Content>): Promise<Content | null> {
    const content = this.content.findOne({ $loki: parseInt(id) })
    if (content) {
      Object.assign(content, updates, { updatedAt: new Date() })
      this.content.update(content)
      return content
    }
    return null
  }

  async deleteContent(id: string): Promise<boolean> {
    const content = this.content.findOne({ $loki: parseInt(id) })
    if (content) {
      this.content.remove(content)
      return true
    }
    return false
  }

  // Settings methods
  async getSetting(key: string): Promise<Settings | null> {
    await this.ensureInitialized()
    if (!this.settings) return null
    return this.settings.findOne({ key })
  }

  async getSettingsByCategory(category: string): Promise<Settings[]> {
    return this.settings.find({ category })
  }

  async getAllSettings(): Promise<Settings[]> {
    return this.settings.find()
  }

  async updateSetting(key: string, value: any, updatedBy: string): Promise<Settings> {
    let setting = this.settings.findOne({ key })
    if (setting) {
      setting.value = value
      setting.updatedAt = new Date()
      setting.updatedBy = updatedBy
      this.settings.update(setting)
    } else {
      setting = this.settings.insert({
        key,
        value,
        category: 'general',
        updatedAt: new Date(),
        updatedBy
      })
    }
    return setting
  }

  // Media methods
  async createMedia(mediaData: Omit<Media, 'id' | 'uploadedAt'>): Promise<Media> {
    const media = {
      ...mediaData,
      uploadedAt: new Date()
    }
    return this.media.insert(media)
  }

  async getAllMedia(): Promise<Media[]> {
    return this.media.find()
  }

  async getMediaById(id: string): Promise<Media | null> {
    return this.media.findOne({ $loki: parseInt(id) })
  }

  async deleteMedia(id: string): Promise<boolean> {
    const media = this.media.findOne({ $loki: parseInt(id) })
    if (media) {
      this.media.remove(media)
      return true
    }
    return false
  }

  // Utility methods
  async getStats() {
    return {
      totalUsers: this.users.count(),
      totalContent: this.content.count(),
      publishedContent: this.content.count({ isPublished: true }),
      totalMedia: this.media.count()
    }
  }

  // Export/Import methods
  exportData() {
    return {
      users: this.users.find(),
      content: this.content.find(),
      settings: this.settings.find(),
      media: this.media.find()
    }
  }

  importData(data: any) {
    if (data.users) {
      this.users.clear()
      data.users.forEach((user: User) => this.users.insert(user))
    }
    if (data.content) {
      this.content.clear()
      data.content.forEach((content: Content) => this.content.insert(content))
    }
    if (data.settings) {
      this.settings.clear()
      data.settings.forEach((setting: Settings) => this.settings.insert(setting))
    }
    if (data.media) {
      this.media.clear()
      data.media.forEach((media: Media) => this.media.insert(media))
    }
  }
}

export const db = new DatabaseService()
export type { User, Content, Settings, Media }
