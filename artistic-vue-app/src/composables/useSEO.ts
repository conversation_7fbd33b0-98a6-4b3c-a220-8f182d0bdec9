import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

interface SEOData {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: string
  siteName?: string
  locale?: string
  author?: string
  publishedTime?: string
  modifiedTime?: string
}

export function useSEO() {
  const { locale } = useI18n()
  
  const defaultSEO: SEOData = {
    title: 'Artistic - Creative Digital Agency',
    description: 'Professional digital agency providing creative solutions for web design, development, and digital marketing.',
    keywords: 'digital agency, web design, web development, creative solutions, marketing',
    image: '/images/og-image.jpg',
    type: 'website',
    siteName: 'Artistic Agency',
    author: 'Artistic Team'
  }

  const updateMetaTags = (seoData: SEOData) => {
    const data = { ...defaultSEO, ...seoData }
    
    // Update title
    if (data.title) {
      document.title = data.title
      updateMetaTag('og:title', data.title)
      updateMetaTag('twitter:title', data.title)
    }

    // Update description
    if (data.description) {
      updateMetaTag('description', data.description)
      updateMetaTag('og:description', data.description)
      updateMetaTag('twitter:description', data.description)
    }

    // Update keywords
    if (data.keywords) {
      updateMetaTag('keywords', data.keywords)
    }

    // Update image
    if (data.image) {
      updateMetaTag('og:image', data.image)
      updateMetaTag('twitter:image', data.image)
    }

    // Update URL
    if (data.url) {
      updateMetaTag('og:url', data.url)
      updateMetaTag('twitter:url', data.url)
      updateLinkTag('canonical', data.url)
    }

    // Update type
    if (data.type) {
      updateMetaTag('og:type', data.type)
    }

    // Update site name
    if (data.siteName) {
      updateMetaTag('og:site_name', data.siteName)
    }

    // Update locale
    updateMetaTag('og:locale', locale.value)

    // Update author
    if (data.author) {
      updateMetaTag('author', data.author)
    }

    // Update published time
    if (data.publishedTime) {
      updateMetaTag('article:published_time', data.publishedTime)
    }

    // Update modified time
    if (data.modifiedTime) {
      updateMetaTag('article:modified_time', data.modifiedTime)
    }

    // Twitter Card
    updateMetaTag('twitter:card', 'summary_large_image')
    updateMetaTag('twitter:site', '@artistic_agency')
  }

  const updateMetaTag = (name: string, content: string) => {
    let element = document.querySelector(`meta[name="${name}"]`) || 
                  document.querySelector(`meta[property="${name}"]`)
    
    if (!element) {
      element = document.createElement('meta')
      if (name.startsWith('og:') || name.startsWith('article:')) {
        element.setAttribute('property', name)
      } else {
        element.setAttribute('name', name)
      }
      document.head.appendChild(element)
    }
    
    element.setAttribute('content', content)
  }

  const updateLinkTag = (rel: string, href: string) => {
    let element = document.querySelector(`link[rel="${rel}"]`)
    
    if (!element) {
      element = document.createElement('link')
      element.setAttribute('rel', rel)
      document.head.appendChild(element)
    }
    
    element.setAttribute('href', href)
  }

  const setSEO = (seoData: SEOData) => {
    updateMetaTags(seoData)
  }

  const setPageSEO = (page: string, customData?: Partial<SEOData>) => {
    const pageData: Record<string, SEOData> = {
      home: {
        title: 'Artistic - Creative Digital Agency | Home',
        description: 'Welcome to Artistic, your premier digital agency for creative web solutions and digital marketing.',
        keywords: 'digital agency, creative solutions, web design, home'
      },
      about: {
        title: 'About Us - Artistic Digital Agency',
        description: 'Learn about our team, mission, and values at Artistic Digital Agency.',
        keywords: 'about us, team, mission, digital agency'
      },
      services: {
        title: 'Our Services - Artistic Digital Agency',
        description: 'Explore our comprehensive digital services including web design, development, and marketing.',
        keywords: 'services, web design, development, digital marketing'
      },
      contact: {
        title: 'Contact Us - Artistic Digital Agency',
        description: 'Get in touch with our team for your next digital project.',
        keywords: 'contact, get in touch, digital agency'
      },
      blog: {
        title: 'Blog - Artistic Digital Agency',
        description: 'Read our latest insights on web design, development, and digital marketing trends.',
        keywords: 'blog, insights, web design, digital marketing'
      },
      projects: {
        title: 'Our Projects - Artistic Digital Agency',
        description: 'Discover our portfolio of successful digital projects and case studies.',
        keywords: 'projects, portfolio, case studies, digital work'
      }
    }

    const seoData = { ...pageData[page], ...customData }
    setSEO(seoData)
  }

  // Watch for locale changes
  watch(locale, () => {
    updateMetaTag('og:locale', locale.value)
  })

  return {
    setSEO,
    setPageSEO,
    updateMetaTags
  }
}
