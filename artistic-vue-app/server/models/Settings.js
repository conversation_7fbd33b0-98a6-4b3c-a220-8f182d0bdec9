const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['general', 'contact', 'social', 'seo', 'theme', 'integrations'],
    trim: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'array', 'object', 'color', 'url', 'email'],
    required: true
  },
  validation: {
    required: { type: Boolean, default: false },
    min: Number,
    max: Number,
    pattern: String,
    options: [String]
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
settingsSchema.index({ category: 1, order: 1 });
settingsSchema.index({ key: 1 });

module.exports = mongoose.model('Settings', settingsSchema);
