const mongoose = require('mongoose');

const contentSchema = new mongoose.Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['text', 'html', 'image', 'video', 'json'],
    required: true
  },
  category: {
    type: String,
    required: true,
    trim: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  content: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  translations: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: new Map()
  },
  metadata: {
    alt: String,
    caption: String,
    seoTitle: String,
    seoDescription: String,
    tags: [String]
  },
  isPublished: {
    type: Boolean,
    default: true
  },
  publishedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better performance
contentSchema.index({ key: 1 });
contentSchema.index({ category: 1 });
contentSchema.index({ type: 1 });
contentSchema.index({ isPublished: 1 });

module.exports = mongoose.model('Content', contentSchema);
