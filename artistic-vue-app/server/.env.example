# Server Configuration
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Database
MONGODB_URI=mongodb://localhost:27017/artistic-cms

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# OpenAI API (for translations and SEO)
OPENAI_API_KEY=your-openai-api-key

# Google Services
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
GOOGLE_SEARCH_CONSOLE_VERIFICATION=your-verification-code

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,video/mp4

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
