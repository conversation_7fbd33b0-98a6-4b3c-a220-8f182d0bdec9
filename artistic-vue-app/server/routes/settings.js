const express = require('express');
const Joi = require('joi');
const Settings = require('../models/Settings');
const auth = require('../middleware/auth');
const { authorize } = require('../middleware/auth');

const router = express.Router();

// Validation schema
const settingsSchema = Joi.object({
  key: Joi.string().required(),
  category: Joi.string().valid('general', 'contact', 'social', 'seo', 'theme', 'integrations').required(),
  title: Joi.string().required(),
  description: Joi.string().allow(''),
  value: Joi.any().required(),
  type: Joi.string().valid('string', 'number', 'boolean', 'array', 'object', 'color', 'url', 'email').required(),
  validation: Joi.object({
    required: Joi.boolean(),
    min: Joi.number(),
    max: Joi.number(),
    pattern: Joi.string(),
    options: Joi.array().items(Joi.string())
  }),
  isPublic: Joi.boolean().default(true),
  order: Joi.number().default(0)
});

// Get public settings
router.get('/public', async (req, res) => {
  try {
    const { category } = req.query;
    
    const filter = { isPublic: true };
    if (category) filter.category = category;

    const settings = await Settings.find(filter)
      .select('-updatedBy')
      .sort({ category: 1, order: 1, title: 1 });

    // Group by category
    const groupedSettings = settings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = {};
      }
      acc[setting.category][setting.key] = setting.value;
      return acc;
    }, {});

    res.json({ settings: groupedSettings });
  } catch (error) {
    console.error('Get public settings error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get all settings (admin)
router.get('/', auth, async (req, res) => {
  try {
    const { category } = req.query;
    
    const filter = {};
    if (category) filter.category = category;

    const settings = await Settings.find(filter)
      .populate('updatedBy', 'username email')
      .sort({ category: 1, order: 1, title: 1 });

    res.json({ settings });
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get single setting
router.get('/:id', auth, async (req, res) => {
  try {
    const setting = await Settings.findById(req.params.id)
      .populate('updatedBy', 'username email');

    if (!setting) {
      return res.status(404).json({ error: 'Setting not found' });
    }

    res.json({ setting });
  } catch (error) {
    console.error('Get setting by ID error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Create setting
router.post('/', auth, authorize(['admin']), async (req, res) => {
  try {
    const { error, value } = settingsSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Check if key already exists
    const existingSetting = await Settings.findOne({ key: value.key });
    if (existingSetting) {
      return res.status(400).json({ error: 'Setting with this key already exists' });
    }

    const setting = new Settings({
      ...value,
      updatedBy: req.user.userId
    });

    await setting.save();
    await setting.populate('updatedBy', 'username email');

    res.status(201).json({ 
      message: 'Setting created successfully',
      setting 
    });
  } catch (error) {
    console.error('Create setting error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Update setting
router.put('/:id', auth, authorize(['admin']), async (req, res) => {
  try {
    const { error, value } = settingsSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const setting = await Settings.findById(req.params.id);
    if (!setting) {
      return res.status(404).json({ error: 'Setting not found' });
    }

    // Check if key is being changed and if new key already exists
    if (value.key !== setting.key) {
      const existingSetting = await Settings.findOne({ key: value.key });
      if (existingSetting) {
        return res.status(400).json({ error: 'Setting with this key already exists' });
      }
    }

    Object.assign(setting, value);
    setting.updatedBy = req.user.userId;
    
    await setting.save();
    await setting.populate('updatedBy', 'username email');

    res.json({ 
      message: 'Setting updated successfully',
      setting 
    });
  } catch (error) {
    console.error('Update setting error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Bulk update settings
router.put('/bulk/update', auth, authorize(['admin']), async (req, res) => {
  try {
    const { settings } = req.body;
    
    if (!Array.isArray(settings)) {
      return res.status(400).json({ error: 'Settings must be an array' });
    }

    const updatePromises = settings.map(async ({ key, value }) => {
      return Settings.findOneAndUpdate(
        { key },
        { value, updatedBy: req.user.userId },
        { new: true }
      );
    });

    const updatedSettings = await Promise.all(updatePromises);

    res.json({ 
      message: 'Settings updated successfully',
      settings: updatedSettings.filter(Boolean) // Remove null results
    });
  } catch (error) {
    console.error('Bulk update settings error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Delete setting
router.delete('/:id', auth, authorize(['admin']), async (req, res) => {
  try {
    const setting = await Settings.findById(req.params.id);
    if (!setting) {
      return res.status(404).json({ error: 'Setting not found' });
    }

    await Settings.findByIdAndDelete(req.params.id);

    res.json({ message: 'Setting deleted successfully' });
  } catch (error) {
    console.error('Delete setting error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get setting categories
router.get('/meta/categories', auth, async (req, res) => {
  try {
    const categories = await Settings.distinct('category');
    res.json({ categories });
  } catch (error) {
    console.error('Get setting categories error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
