const express = require('express');
const Joi = require('joi');
const Content = require('../models/Content');
const auth = require('../middleware/auth');
const { authorize } = require('../middleware/auth');

const router = express.Router();

// Validation schemas
const contentSchema = Joi.object({
  key: Joi.string().required(),
  type: Joi.string().valid('text', 'html', 'image', 'video', 'json').required(),
  category: Joi.string().required(),
  title: Joi.string().required(),
  description: Joi.string().allow(''),
  content: Joi.any().required(),
  translations: Joi.object().pattern(Joi.string(), Joi.any()),
  metadata: Joi.object({
    alt: Joi.string().allow(''),
    caption: Joi.string().allow(''),
    seoTitle: Joi.string().allow(''),
    seoDescription: Joi.string().allow(''),
    tags: Joi.array().items(Joi.string())
  }),
  isPublished: Joi.boolean().default(true)
});

// Get all content (public endpoint for published content)
router.get('/public', async (req, res) => {
  try {
    const { category, type, lang = 'en' } = req.query;
    
    const filter = { isPublished: true };
    if (category) filter.category = category;
    if (type) filter.type = type;

    const content = await Content.find(filter)
      .select('-createdBy -updatedBy')
      .sort({ category: 1, key: 1 });

    // Transform content with translations
    const transformedContent = content.map(item => {
      const result = item.toObject();
      
      // If translation exists for requested language, use it
      if (lang !== 'en' && result.translations && result.translations.get(lang)) {
        const translation = result.translations.get(lang);
        if (typeof translation === 'object' && translation.content) {
          result.content = translation.content;
        }
        if (translation.title) result.title = translation.title;
        if (translation.description) result.description = translation.description;
      }
      
      delete result.translations; // Remove translations from public response
      return result;
    });

    res.json({ content: transformedContent });
  } catch (error) {
    console.error('Get public content error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get all content (admin)
router.get('/', auth, async (req, res) => {
  try {
    const { category, type, search, page = 1, limit = 50 } = req.query;
    
    const filter = {};
    if (category) filter.category = category;
    if (type) filter.type = type;
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { key: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * limit;
    
    const content = await Content.find(filter)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Content.countDocuments(filter);

    res.json({ 
      content,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get content error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get single content item
router.get('/:id', auth, async (req, res) => {
  try {
    const content = await Content.findById(req.params.id)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email');

    if (!content) {
      return res.status(404).json({ error: 'Content not found' });
    }

    res.json({ content });
  } catch (error) {
    console.error('Get content by ID error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Create content
router.post('/', auth, authorize(['admin', 'editor']), async (req, res) => {
  try {
    const { error, value } = contentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Check if key already exists
    const existingContent = await Content.findOne({ key: value.key });
    if (existingContent) {
      return res.status(400).json({ error: 'Content with this key already exists' });
    }

    const content = new Content({
      ...value,
      createdBy: req.user.userId
    });

    await content.save();
    await content.populate('createdBy', 'username email');

    res.status(201).json({ 
      message: 'Content created successfully',
      content 
    });
  } catch (error) {
    console.error('Create content error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Update content
router.put('/:id', auth, authorize(['admin', 'editor']), async (req, res) => {
  try {
    const { error, value } = contentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const content = await Content.findById(req.params.id);
    if (!content) {
      return res.status(404).json({ error: 'Content not found' });
    }

    // Check if key is being changed and if new key already exists
    if (value.key !== content.key) {
      const existingContent = await Content.findOne({ key: value.key });
      if (existingContent) {
        return res.status(400).json({ error: 'Content with this key already exists' });
      }
    }

    Object.assign(content, value);
    content.updatedBy = req.user.userId;
    
    await content.save();
    await content.populate(['createdBy', 'updatedBy'], 'username email');

    res.json({ 
      message: 'Content updated successfully',
      content 
    });
  } catch (error) {
    console.error('Update content error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Delete content
router.delete('/:id', auth, authorize(['admin']), async (req, res) => {
  try {
    const content = await Content.findById(req.params.id);
    if (!content) {
      return res.status(404).json({ error: 'Content not found' });
    }

    await Content.findByIdAndDelete(req.params.id);

    res.json({ message: 'Content deleted successfully' });
  } catch (error) {
    console.error('Delete content error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get content categories
router.get('/meta/categories', auth, async (req, res) => {
  try {
    const categories = await Content.distinct('category');
    res.json({ categories });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
