const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const auth = require('../middleware/auth');
const { authorize } = require('../middleware/auth');

const router = express.Router();

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '../uploads');
fs.mkdir(uploadsDir, { recursive: true }).catch(console.error);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(uploadsDir, file.fieldname || 'general');
    fs.mkdir(uploadPath, { recursive: true })
      .then(() => cb(null, uploadPath))
      .catch(err => cb(err));
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext)
      .replace(/[^a-zA-Z0-9]/g, '-')
      .toLowerCase();
    cb(null, `${name}-${uniqueSuffix}${ext}`);
  }
});

// File filter
const fileFilter = (req, file, cb) => {
  const allowedTypes = {
    'image/jpeg': true,
    'image/jpg': true,
    'image/png': true,
    'image/gif': true,
    'image/webp': true,
    'image/svg+xml': true,
    'video/mp4': true,
    'video/webm': true,
    'video/ogg': true,
    'application/pdf': true,
    'text/plain': true,
    'application/json': true
  };

  if (allowedTypes[file.mimetype]) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} is not allowed`), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 10 // Max 10 files per request
  }
});

// Upload single file
router.post('/upload', auth, authorize(['admin', 'editor']), upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const fileInfo = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: req.file.path,
      url: `/uploads/${req.file.fieldname || 'general'}/${req.file.filename}`,
      uploadedBy: req.user.userId,
      uploadedAt: new Date()
    };

    res.json({
      message: 'File uploaded successfully',
      file: fileInfo
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

// Upload multiple files
router.post('/upload-multiple', auth, authorize(['admin', 'editor']), upload.array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    const filesInfo = req.files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      url: `/uploads/${file.fieldname || 'general'}/${file.filename}`,
      uploadedBy: req.user.userId,
      uploadedAt: new Date()
    }));

    res.json({
      message: 'Files uploaded successfully',
      files: filesInfo
    });
  } catch (error) {
    console.error('Multiple upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

// Get uploaded files list
router.get('/files', auth, async (req, res) => {
  try {
    const { type, page = 1, limit = 20 } = req.query;
    
    // This is a simple implementation. In production, you'd want to store file metadata in database
    const files = [];
    const categories = await fs.readdir(uploadsDir);
    
    for (const category of categories) {
      const categoryPath = path.join(uploadsDir, category);
      const stat = await fs.stat(categoryPath);
      
      if (stat.isDirectory()) {
        const categoryFiles = await fs.readdir(categoryPath);
        
        for (const filename of categoryFiles) {
          const filePath = path.join(categoryPath, filename);
          const fileStat = await fs.stat(filePath);
          
          if (fileStat.isFile()) {
            const ext = path.extname(filename).toLowerCase();
            const isImage = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(ext);
            const isVideo = ['.mp4', '.webm', '.ogg'].includes(ext);
            
            if (!type || 
                (type === 'image' && isImage) || 
                (type === 'video' && isVideo) ||
                (type === 'document' && !isImage && !isVideo)) {
              
              files.push({
                filename,
                category,
                url: `/uploads/${category}/${filename}`,
                size: fileStat.size,
                createdAt: fileStat.birthtime,
                modifiedAt: fileStat.mtime,
                type: isImage ? 'image' : isVideo ? 'video' : 'document'
              });
            }
          }
        }
      }
    }

    // Sort by creation date (newest first)
    files.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedFiles = files.slice(startIndex, endIndex);

    res.json({
      files: paginatedFiles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: files.length,
        pages: Math.ceil(files.length / limit)
      }
    });
  } catch (error) {
    console.error('Get files error:', error);
    res.status(500).json({ error: 'Failed to get files' });
  }
});

// Delete file
router.delete('/files/:category/:filename', auth, authorize(['admin', 'editor']), async (req, res) => {
  try {
    const { category, filename } = req.params;
    const filePath = path.join(uploadsDir, category, filename);

    // Check if file exists
    try {
      await fs.access(filePath);
    } catch {
      return res.status(404).json({ error: 'File not found' });
    }

    // Delete file
    await fs.unlink(filePath);

    res.json({ message: 'File deleted successfully' });
  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({ error: 'Failed to delete file' });
  }
});

// Error handling for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 10MB.' });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ error: 'Too many files. Maximum is 10 files.' });
    }
  }
  
  if (error.message.includes('File type')) {
    return res.status(400).json({ error: error.message });
  }

  next(error);
});

module.exports = router;
