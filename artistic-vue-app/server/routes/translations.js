const express = require('express');
const Joi = require('joi');
const auth = require('../middleware/auth');
const { authorize } = require('../middleware/auth');

const router = express.Router();

// Mock OpenAI integration for translations
// In production, you would use actual OpenAI API
const translateText = async (text, targetLanguage, sourceLanguage = 'en') => {
  // This is a mock implementation
  // Replace with actual OpenAI API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(`[${targetLanguage.toUpperCase()}] ${text}`);
    }, 1000);
  });
};

// Validation schema
const translateSchema = Joi.object({
  text: Joi.string().required(),
  targetLanguage: Joi.string().required(),
  sourceLanguage: Joi.string().default('en'),
  context: Joi.string().allow('')
});

const batchTranslateSchema = Joi.object({
  texts: Joi.array().items(Joi.object({
    key: Joi.string().required(),
    text: Joi.string().required(),
    context: Joi.string().allow('')
  })).required(),
  targetLanguage: Joi.string().required(),
  sourceLanguage: Joi.string().default('en')
});

// Translate single text
router.post('/translate', auth, authorize(['admin', 'editor']), async (req, res) => {
  try {
    const { error, value } = translateSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const { text, targetLanguage, sourceLanguage, context } = value;

    // In production, use OpenAI API
    const translatedText = await translateText(text, targetLanguage, sourceLanguage);

    res.json({
      originalText: text,
      translatedText,
      sourceLanguage,
      targetLanguage,
      context
    });
  } catch (error) {
    console.error('Translation error:', error);
    res.status(500).json({ error: 'Translation failed' });
  }
});

// Batch translate multiple texts
router.post('/translate-batch', auth, authorize(['admin', 'editor']), async (req, res) => {
  try {
    const { error, value } = batchTranslateSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const { texts, targetLanguage, sourceLanguage } = value;

    const translations = await Promise.all(
      texts.map(async (item) => {
        const translatedText = await translateText(item.text, targetLanguage, sourceLanguage);
        return {
          key: item.key,
          originalText: item.text,
          translatedText,
          context: item.context
        };
      })
    );

    res.json({
      translations,
      sourceLanguage,
      targetLanguage,
      totalCount: translations.length
    });
  } catch (error) {
    console.error('Batch translation error:', error);
    res.status(500).json({ error: 'Batch translation failed' });
  }
});

// Get supported languages
router.get('/languages', auth, (req, res) => {
  const supportedLanguages = [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'tr', name: 'Turkish', nativeName: 'Türkçe' },
    { code: 'de', name: 'German', nativeName: 'Deutsch' },
    { code: 'fr', name: 'French', nativeName: 'Français' },
    { code: 'es', name: 'Spanish', nativeName: 'Español' },
    { code: 'it', name: 'Italian', nativeName: 'Italiano' },
    { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
    { code: 'ru', name: 'Russian', nativeName: 'Русский' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語' },
    { code: 'ko', name: 'Korean', nativeName: '한국어' },
    { code: 'zh', name: 'Chinese', nativeName: '中文' },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية' }
  ];

  res.json({ languages: supportedLanguages });
});

// Auto-translate content for specific language
router.post('/auto-translate/:language', auth, authorize(['admin']), async (req, res) => {
  try {
    const { language } = req.params;
    const { contentIds } = req.body;

    if (!contentIds || !Array.isArray(contentIds)) {
      return res.status(400).json({ error: 'contentIds array is required' });
    }

    // This would integrate with your Content model
    // For now, return a mock response
    const results = contentIds.map(id => ({
      contentId: id,
      status: 'translated',
      language,
      translatedFields: ['title', 'description', 'content']
    }));

    res.json({
      message: `Auto-translation to ${language} completed`,
      results,
      totalProcessed: results.length
    });
  } catch (error) {
    console.error('Auto-translate error:', error);
    res.status(500).json({ error: 'Auto-translation failed' });
  }
});

// Generate SEO content with AI
router.post('/generate-seo', auth, authorize(['admin', 'editor']), async (req, res) => {
  try {
    const { title, description, keywords, language = 'en' } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Title is required' });
    }

    // Mock AI-generated SEO content
    const seoContent = {
      metaTitle: `${title} | Professional Services`,
      metaDescription: description || `Discover ${title} - professional solutions tailored to your needs.`,
      keywords: keywords || ['professional', 'services', 'quality', 'expert'],
      ogTitle: title,
      ogDescription: description || `Learn more about ${title} and how we can help you.`,
      structuredData: {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": title,
        "description": description
      }
    };

    res.json({
      message: 'SEO content generated successfully',
      seoContent,
      language
    });
  } catch (error) {
    console.error('Generate SEO error:', error);
    res.status(500).json({ error: 'SEO generation failed' });
  }
});

module.exports = router;
