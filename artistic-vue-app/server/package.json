{"name": "artistic-admin-api", "version": "1.0.0", "description": "Admin API for Artistic Vue App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["admin", "api", "vue", "cms"], "author": "Artistic Agency", "license": "MIT"}